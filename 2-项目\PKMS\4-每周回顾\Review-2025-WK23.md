---
homePageLink: "[[PKMS-首页]]"
---
# 1. 阻碍分析
```dataviewjs
	const projectName = dv.current().file.path.split("/")[1].trim();
	const folder = `2-项目/${projectName}/2-每日执行`;
	
	// 1. 从当前文件名提取周数
	const currentFileName = dv.current().file.name;
	const replayMatch = currentFileName.match(/Review-(\d{4})-WK(\d{2})/);
	
	//数组解构赋值，replayMatch的值为["Replay-2025-WK22", "2025", "22", index: 0, input: "Replay-2025-WK22", groups: undefined]，其中[,]第一参数表示“跳过第一个元素”
	const [, currentYear, currentWeekStr] = replayMatch;
	const currentWeek = parseInt(currentWeekStr);
	
	// 2. 筛选目标文件
	const tasks = dv.pages(`"${folder}"`).file
		.filter(file => {
			const match = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);//示例["Do-2025-06-08", "2025", "06", "08"]
			if (!match) return false;
			
			const date = moment(match.slice(1, 4).join('-'));
			const fileYear = date.isoWeekYear();
			const fileWeek = date.isoWeek();
			
			return fileYear.toString() === currentYear && fileWeek <= currentWeek;
		})
		.flatMap(file => file.tasks || []) // 正确访问每个文件的tasks
		.filter(t => t.text) // 过滤有文本的任务
		.filter(t => t.section?.subpath?.toLowerCase().includes("阻碍")); // 过滤"闪念"标题下的任务
	// 3. 显示结果
	if (tasks.length > 0) {
		dv.el("p", `截止第${currentWeek}周的数据汇总`);
		dv.taskList(tasks, false);
	} else {
		dv.el("p", `📭 未找到阻碍`);
	}
```
# 2. 效能分析

- 
