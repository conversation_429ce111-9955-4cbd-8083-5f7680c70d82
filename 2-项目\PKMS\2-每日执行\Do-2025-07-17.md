---
homePageLink: "[[PKMS-首页]]"
---
# 1. 任务安排
> [!dashboard]
> 
> > [!todo] 今日代办
> > ```tasks
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const titleKeywords = "任务"; \
> > const headingMatches = task.heading && task.heading.includes(titleKeywords);\
> > let dateMatches = false;\
> > if (task.scheduled && task.scheduled.moment) {\
> > dateMatches = task.scheduled.moment.isSame(targetDate, 'day')}\
> > return pathMatches && headingMatches && dateMatches;
> > ```
> 
> > [!tip] 未规划
> > ```tasks
> > not done
> > no scheduled date
> > hide backlink
> > filter by function \
> > const fileName = query.file.filenameWithoutExtension;\
> > const dateMatch = fileName.match(/\d{4}-\d{2}-\d{2}/);\
> > if (!dateMatch) return false;\
> > const targetDate = moment(dateMatch[0]);\
> > const year = targetDate.isoWeekYear();\
> > const weekNumber = targetDate.isoWeek().toString().padStart(2, '0');\
> > const targetPath = `Plan-${year}-WK${weekNumber}`;\
> > const pathMatches = task.file.path.includes(targetPath);\
> > const titleKeywords = "任务";\
> > const headingMatches = task.heading && task.heading.includes(titleKeywords);\
> > const hasContent = task.description?.trim().length > 0;\
> > return pathMatches && headingMatches && hasContent;
> > ```
# 2. 今日阻碍
```dataviewjs
// 1. 从当前文件名提取目标日期
const currentFileName = dv.current().file.name;
const dateMatch = currentFileName.match(/Do-(\d{4}-\d{2}-\d{2})/);
if (dateMatch && dateMatch[1]) {
    const targetDate = dateMatch[1];
    const targetDateObj = dv.date(targetDate); // 创建目标日期对象
    // 2. 从文件路径提取项目名称
    const currentPath = dv.current().file.path;
    const projectName = currentPath.split("/")[1];
    const path = `3-过程资产/${projectName}/阻碍`;
    // 4. 查询目标文件
	const files = dv.pages(`"${path}"`).filter(p => {
		if (!p.file?.name) return false;
		
		// 文件名格式检查 (blocker-20250711-01)
		if (!p.file.name.match(/^blocker-\d{8}-\d{2}$/)) return false;
		
		// 提取文件名中的日期部分
		const fileDateMatch = p.file.name.match(/^blocker-(\d{4})(\d{2})(\d{2})-\d{2}$/);
		if (!fileDateMatch) return false;
		
		// 创建文件日期对象
		const fileDateStr = `${fileDateMatch[1]}-${fileDateMatch[2]}-${fileDateMatch[3]}`;
		const fileDateObj = dv.date(fileDateStr);
		
		// 比较日期
		return fileDateObj?.isValid && fileDateObj.hasSame(targetDateObj, "day");
	});
	
	// 5. 结果输出
	if (files.length > 0) {
		for (let file of files) {
			const linkText = file.aliases?.length ? file.aliases[0] : file.file.name;
			const status = file.status || "未设置";
			const relation = file.relation ? file.relation : "未设置";
			
			dv.paragraph(`- [[${file.file.path}|${linkText}]] - 🚦${status} - 🧷${relation}`);
		}
	} else {
		dv.el("p","📭 今日未遇到相关阻碍");
	}
}else{
	dv.el("p","❌ 当前文件名称错误，正确为 Do-YYYY-MM-DD")
}
```
# 3. 输出

- 「每周计划」模板（删除看板，显示所有状态任务，显示依赖任务）
- 「[[阻碍处理工作流程图]]」