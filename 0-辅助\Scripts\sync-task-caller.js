// 任务同步调用器
// 功能：提供一个简单的接口来调用 sync-task.js 脚本

module.exports = async function(tp) {
  try {
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("❌ 请先打开一个文件");
      return null;
    }

    // 获取 sync-task 脚本
    const syncTaskScript = require('./sync-task.js');
    
    // 创建执行上下文
    const context = {
      app: {
        app: app,
        moment: moment,
        file: activeFile,
        workspace: app.workspace
      }
    };

    // 执行同步任务脚本
    await syncTaskScript.call(context, {});
    
    return null; // 不返回任何内容到编辑器
    
  } catch (error) {
    console.error("调用任务同步脚本时出错:", error);
    new Notice(`❌ 调用失败: ${error.message}`);
    return null;
  }
};
