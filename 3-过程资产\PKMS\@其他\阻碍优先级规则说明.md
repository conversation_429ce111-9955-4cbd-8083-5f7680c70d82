## 一、核心公式

**适用场景**：阻塞型阻碍优先级计算

**优先级 = 类型权重 × (1+√(存活迭代 x 迭代频率/16) × log₂(强化频率值+1.2) × (1+分级场景系数)**

**核心目标**：通过“类型权重（原因）+ 关联字段（对象）”双维度协同，将阻塞型阻碍趋同率控制在5%以内，同时确保“阻碍类型”与“关联字段”相互独立。

## 二、参数详情

### 2.1 类型权重（Type Weight）

**定义**：阻碍的**静态优先级基础值**，**仅基于产生原因判定**（与关联对象无关），反映问题的**本质复杂性**。

|类型名称|权重值|适用场景|配置方式|
|---|---|---|---|
|硬阻塞|1.3|导致功能完全不可用的阻塞（如API接口宕机、数据库连接失败）|手动标记（如“阻塞型”标签）|
|价值威胁|1.0|常规功能缺陷或性能问题（如UI显示错位、加载速度慢）|默认值（未标记为硬阻塞/复合型）|
|**复合型**|**1.6**|**跨系统/多模块的复杂阻碍**（如跨系统API数据同步失败、多模块依赖冲突）|**基于产生原因判定**（如“跨系统”“多模块”标签或问题描述）|
### 2.2 存活迭代（Age Iterations）

**定义**：阻碍从创建到当前经历的迭代次数，反映问题的**老化程度**（存活时间越长，优先级越高）。

**计算方式**： 存活迭代=当前迭代号−创建迭代号+1存活迭代=当前迭代号−创建迭代号+1

**时间因子**（老化惩罚）： 时间因子=1+√(存活迭代 x 迭代频率/16)

|存活迭代|时间因子|业务含义|
|---|---|---|
|1|1.25|新阻碍（1个迭代内）|
|4|1.50|中期阻碍（2-3个迭代）|
|9|1.75|长期阻碍（4-5个迭代）|
|16|2.00|超期阻碍（≥8个迭代）|

### 2.3 强化频率值（Enhanced Frequency）

**定义**：**仅基于关联对象的影响力**，反映阻碍的**扩散范围**（关联对象越多、类型越重要，优先级越高）。

**计算方式**： 强化频率值=∑(类型基数ⁿ × 数量 x 迭代频率)其中：

- n=min⁡(3,关联类型数)n=min(3,关联类型数)（关联类型数越多，指数放大效果越显著，上限为3）；
- **类型基数**：关联对象的**影响力权重**（越大表示该对象对项目的影响越大）。

|关联类型|基数|说明|
|---|---|---|
|任务（Task）|1.2|常规开发任务（如“用户登录功能开发”）|
|阻碍（Blockage）|1.5|被其他阻碍依赖（如“API阻塞导致支付功能阻碍”）|
|周计划（Plan）|2.0|迭代计划中标记的阻碍（如“本周计划完成的订单模块阻塞”）|
|周评审（Review）|2.5|评审会议中提出的问题（如“周评审时发现的性能瓶颈”）|
|周回顾（Retro）|2.2|回顾会议中识别的改进项（如“周回顾时发现的流程缺陷”）|

**示例**： 关联2个任务（Task）+1个周评审（Review）→ 关联类型数=2 → n=2 → 强化频率值= (1.2²×2)+(2.5²×1)=2.88+6.25=9.13

### 2.4 分级场景系数（Tiered Scene Coefficient）

**定义**：**仅基于关联对象的紧急度**，反映阻碍的**时间敏感性**（关联会议越多、数量越多，优先级越高）。

**计算方式**： 场景系数=min⁡(0.10 (基础值)+会议基础加成+多类型加成+关联数量加成,0.85 (上限))场景系数=min(0.10(基础值)+会议基础加成+多类型加成+关联数量加成,0.85(上限))

|组成部分|计算规则|
|---|---|
|基础值|固定0.10（无会议关联时的默认值）|
|会议基础加成|按**最高优先级**会议类型取值：周评审（+0.30）> 周回顾（+0.25）> 周计划（+0.15）|
|多类型加成|每增加1种会议类型（如周计划+周评审）：+0.15|
|关联数量加成|每增加1个关联（无论类型）：+0.10（总关联数-1=额外关联数）|

**示例**： 关联1个周计划（Plan）+1个周评审（Review）+2个任务（Task）→ 总关联数=4 → 场景系数= 0.10+0.30 (周评审)+0.15 (2种会议)+0.30 (3个额外关联)=0.850.10+0.30(周评审)+0.15(2种会议)+0.30(3个额外关联)=0.85（触发上限）。

## 三、示例验证

### 示例1：跨系统API阻塞（复合型）

**场景**：跨系统API数据同步失败（涉及用户、订单、支付模块），**类型为复合型（1.6）**，存活迭代=4，关联2个任务（Task）+1个周评审（Review）。

**计算过程**：

1. 类型权重：1.6（仅基于跨系统产生原因）；
2. 时间因子：1+(4/16)=1.501+(​4/16)=1.50；
3. 强化频率值：(1.22×2)+(2.52×1)=9.13(1.22×2)+(2.52×1)=9.13；
4. 频率因子：log⁡2(9.13+1.2)=log⁡2(10.33)≈3.37log2​(9.13+1.2)=log2​(10.33)≈3.37；
5. 场景系数：0.10+0.30 (周评审)+0 (1种会议)+0.20 (2个额外关联)=0.600.10+0.30(周评审)+0(1种会议)+0.20(2个额外关联)=0.60；
6. **最终优先级**：1.6×1.50×3.37×(1+0.60)=13.011.6×1.50×3.37×(1+0.60)=13.01。

### 示例2：常规UI显示异常（价值威胁）

**场景**：用户头像显示错位，**类型为价值威胁（1.0）**，存活迭代=1，关联1个任务（Task）。

**计算过程**：

1. 类型权重：1.0（默认值）；
2. 时间因子：1+(1/16)=1.251+(​1/16)=1.25；
3. 强化频率值：1.21×1=1.21.21×1=1.2；
4. 频率因子：log⁡2(1.2+1.2)=log⁡2(2.4)≈1.26log2​(1.2+1.2)=log2​(2.4)≈1.26；
5. 场景系数：0.10+0 (无会议)+0 (0种会议)+0 (0个额外关联)=0.100.10+0(无会议)+0(0种会议)+0(0个额外关联)=0.10；
6. **最终优先级**：1.0×1.25×1.26×(1+0.10)=1.731.0×1.25×1.26×(1+0.10)=1.73。

**结论**： 复合型阻碍（示例1，优先级13.01）**远高于**价值威胁阻碍（示例2，优先级1.73），即使关联数量少，仍能通过**类型权重（1.6）**准确反映其**本质复杂性**；关联字段（任务、周评审）仅影响**强化频率值**和**场景系数**，不干扰类型权重的判定。

## 四、规则说明

### 4.1 类型与关联独立原则

- **类型权重**：仅基于阻碍的**产生原因**（如跨系统/多模块），与关联对象无关；
- **关联字段**：仅基于阻碍的**关联对象**（如任务、周评审），与产生原因无关；
- 两者协同作用，既反映问题的**本质复杂性**（类型权重），又反映问题的**扩散范围与紧急度**（关联字段）。

### 4.2 动态调整机制

- 当阻塞型阻碍趋同率超过5%时，系统将**自动调整类型基数**（如任务基数从1.2→1.3），以提高区分度；
- 调整仅影响**强化频率值**（关联对象的影响力），不改变**类型权重**（产生原因的本质复杂性）。

### 4.3 数据来源

- 迭代号、关联类型、存活迭代等数据从项目管理工具（如Jira）**自动同步**，确保计算准确性；
- 类型权重（如复合型）需**人工校验**（如通过问题描述或标签确认），避免误判。

## 五、版本说明

|版本|修订内容|日期|
|---|---|---|
|4.0|1. 移除复合型阻碍“需关联≥3种类型”的强制要求；2. 明确“类型权重与关联字段独立”原则；3. 调整示例以反映新逻辑|2025-08-XX|
|3.0|修正场景系数计算逻辑，明确多类型加成规则|2025-08-06|
|2.0|新增强化频率值的指数计算规则|2025-07-28|
|1.0|初始版本发布|2025-07-15|

**备注**：本规则修订后，仍保持对阻塞型阻碍的高区分度，趋同率控制在5%以内，同时解决了“类型与关联绑定”的逻辑漏洞。