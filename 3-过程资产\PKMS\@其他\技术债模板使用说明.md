# 技术债模板使用说明

## 概述

技术债模板（`TP-Project-技术债.md`）是用于系统化记录和管理技术债务的标准化工具。本模板基于实际使用经验优化设计，旨在帮助团队快速、准确地识别、记录和跟踪技术债务的偿还过程。

## 一、元数据字段定义及填写依据

| 字段名          | 定义           | 格式                                                                     | 填写依据                                                                      | 作用                   |
| ------------ | ------------ | ---------------------------------------------------------------------- | ------------------------------------------------------------------------- | -------------------- |
| created_Date | 技术债发现并记录的日期  | YYYY-MM-DD（如：2025-08-09）                                               | 系统自动填充当前日期，无需手动修改                                                         | 用于债务生命周期追踪和统计分析      |
| aliases      | 技术债的简短别名或标题  | 数组形式，如：`["技术债模板不可用"]`                                                  | 1、使用简洁明了的描述性语言<br>2、突出核心问题或影响<br>3、便于在链接和搜索中快速识别                          | 提高文档可读性和检索效率         |
| work_Item    | 发现技术债的具体对象位置 | 链接数组，如：`[["[[TP-Project-技术债]]"]]`                                      | 关联到具体发现位置                                                                 | 建立债务与对象间的关联关系，便于整体管理 |
| type         | 技术债的类型分类     | `局部债`：影响范围限定在特定模块或功能<br>`系统债`：影响整个系统架构或多个模块                            | 1、评估影响范围：单一模块选择"局部债"<br>2、跨模块或架构层面选择"系统债"<br>3、评估时间≤3秒，基于直观判断             | 指导偿还策略和资源分配          |
| priority     | 技术债的优先级等级    | `P0`：立即处理（阻塞生产或关键功能）<br>`P1`：高优先级（显著影响性能或稳定性）<br>`P2`：中等优先级（有影响但可暂时接受） | `P0`：生产环境阻塞、安全漏洞、数据丢失风险<br>  `P1`：性能显著下降、用户体验严重影响<br>  `P2`：代码质量问题、维护成本增加 | 确定处理顺序和资源投入          |
| status       | 技术债当前的处理状态   | `待处理`：已识别但未开始处理<br>  `进行中`：正在积极偿还<br>  `已关闭`：已完成偿还或决定不处理               | 根据实际工作状态实时更新                                                              | 跟踪处理进度，支持状态统计        |
| cssclasses   | 文档元数据的CSS样式类 | 默认值：`c3`                                                               | 保持默认值，用于统一文档样式                                                            | 确保技术债文档的视觉一致性        |
## 二、模板正文模块说明

### 2.1 基础信息模块

- 核心作用：建立技术债的基本画像，为后续分析和决策提供事实基础

| 模块    | 目的            | 填写要点                                                                                    |
| ----- | ------------- | --------------------------------------------------------------------------------------- |
| 场景描述  | 明确技术债发生的具体情境  | 1、描述具体的业务场景或操作流程<br>2、避免技术术语，使用业务语言<br>3、示例：`根据模板记录技术债时`                                |
| 触发条件  | 识别导致问题暴露的具体条件 | 1、列出所有相关的前置条件<br>2、包括环境、数据、操作等因素<br>3、使用项目符号分层描述<br>4、示例：判断技术债类型、记录基础信息等                |
| 可观测现象 | 记录问题的外在表现     | 1、描述用户或开发者能直接观察到的现象<br>2、量化描述（如时间、频率、影响范围）<br>3、避免推测，只记录事实<br>4、示例：`很难快速得出结论并完成元数据字段填写` |
| 关键影响  | 评估技术债的业务和技术影响 | 1、关注对业务目标的影响<br>2、包括短期和长期影响<br>3、量化影响程度<br>4、示例：`技术债记录中断，后续迭代无法正常开展`                    |
### 2.2 应急方案模块

- 核心作用：在技术债完全偿还前，提供临时缓解措施，确保业务连续性
- 表格填写要点
	- `生效时间`：应急方案开始执行的时间
	- `行动`：具体的临时措施
	- `退出条件`：停止应急方案的明确标准
	- `状态`：当前方案的执行状态，建议≤14天
- 设计原则
	- 快速实施，降低immediate风险
	- 明确退出条件，避免临时方案固化
	- 定期评估有效性
	- 示例：停止详细记录，仅填写关键信息
### 2.3 根因分析模块

- 核心作用：深入分析技术债产生的根本原因，为制定有效的偿还策略提供依据
- 分析方法
	- 使用"5个为什么"或鱼骨图方法
	- 从表面现象逐步深入到根本原因
	- 区分直接原因和根本原因
### 2.4 偿还计划模块

核心作用：制定系统化的技术债偿还路径，确保有序推进和进度跟踪。

填写要点
- 任务设计
	- 每个任务应该是可执行的具体行动
	- 设置明确的完成标准
	- 合理估算时间和资源需求
- 进度跟踪
	- 使用checkbox标记完成状态
	- 记录实际完成时间
	- 及时更新任务状态

示例格式
```markdown
- [x] 重新设计模板type、priority字段 ➕ 2025-08-12 ✅ 2025-08-13
- [ ] 利用实际案例测试模板合理性 ➕ 2025-08-12
```
### 2.5 债务谱系模块

核心作用：可视化展示技术债之间的关联关系，支持系统性的债务管理。

填写要点

- 关系类型
	- 因果关系：A导致B
	- 依赖关系：A的解决依赖于B
	- 并行关系：A和B可以同时处理
- 图表设计
	- 使用Mermaid语法绘制关系图
	- 保持图表简洁清晰
	- 突出关键路径
### 2.6 验收清单模块

核心作用：定义技术债偿还完成的明确标准，确保解决方案的有效性

填写要点
- 标准制定
	- 可测量：使用具体的数值指标
	- 可验证：能够通过测试或观察确认
	- 业务相关：与实际使用场景对应
- 示例标准
	- 性能指标：`模板每个元数据字段评估时间≤3s`
	- 用户体验：`正文模块内容符合直觉流，且未频繁出现思维断裂`

## 三、使用建议

- 先填写基础信息，建立问题画像
- 设计应急方案，确保业务连续性
- 进行根因分析，找到根本原因
- 制定偿还计划，明确行动路径
- 绘制债务谱系，理解关联关系
- 定义验收清单，确保解决标准