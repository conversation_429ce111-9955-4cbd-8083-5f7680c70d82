// 测试 Tasks 命令执行
// 功能：专门测试 Tasks 插件的命令执行

module.exports = async function (tp) {
  try {
    console.log("=== 测试 Tasks 命令执行 ===");
    
    // 获取当前活动文件和编辑器
    const activeFile = app.workspace.getActiveFile();
    const activeView = app.workspace.getActiveViewOfType(MarkdownView);
    
    if (!activeFile || !activeView) {
      new Notice("❌ 请先打开一个 Markdown 文件");
      return null;
    }
    
    console.log("当前文件:", activeFile.path);
    console.log("当前视图类型:", activeView.getViewType());
    
    // 确保编辑器有焦点
    const editor = activeView.editor;
    if (editor) {
      console.log("编辑器状态:");
      console.log("  - 光标位置:", editor.getCursor());
      console.log("  - 选中文本:", editor.getSelection());
      console.log("  - 文档长度:", editor.getValue().length);
      
      // 将光标移动到文档末尾
      const lastLine = editor.lastLine();
      editor.setCursor(lastLine, editor.getLine(lastLine).length);
      console.log("光标已移动到文档末尾");
    }
    
    // 测试 Tasks 命令
    const taskCommandId = "obsidian-tasks-plugin:edit-task";
    
    console.log(`准备执行命令: ${taskCommandId}`);
    
    try {
      // 确保编辑器获得焦点
      activeView.editor.focus();
      
      // 等待一小段时间确保焦点设置完成
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 执行命令
      const result = await app.commands.executeCommandById(taskCommandId);
      console.log("命令执行结果:", result);
      
      // 检查是否有模态框打开
      setTimeout(() => {
        const modals = document.querySelectorAll('.modal');
        console.log(`检测到 ${modals.length} 个模态框`);
        modals.forEach((modal, index) => {
          console.log(`模态框 ${index + 1}:`, modal.className);
        });
        
        if (modals.length === 0) {
          console.log("⚠️ 没有检测到模态框，可能命令没有正常工作");
          new Notice("⚠️ Tasks 创建窗口可能没有正常打开", 3000);
        } else {
          console.log("✅ 检测到模态框，命令可能执行成功");
          new Notice("✅ 检测到 Tasks 窗口", 3000);
        }
      }, 500);
      
      new Notice("✅ Tasks 命令已执行，请检查是否有窗口打开", 5000);
      
    } catch (error) {
      console.error("执行命令时出错:", error);
      new Notice(`❌ 命令执行失败: ${error.message}`);
    }
    
    return null;
    
  } catch (error) {
    console.error("测试脚本出错:", error);
    new Notice(`❌ 测试失败: ${error.message}`);
    return null;
  }
};
