---
aliases:
  - 目标制定方法缺失
created_Date: 2025-07-10
status: 进行中
relation:
  - "[[Plan-2025-WK27]]"
  - "[[blocker-20250602-01]]"
cssclasses:
  - c3
type: 阻塞型
---
# 1. 基础信息

- 现象描述：知道核心目标是什么，但是不清楚如何准确描述？
- 直接影响：周目标制定工作中断
- 衍生影响：迭代进度延迟、任务优先级混乱及资源分配效率降低

# 2. 临时方案

| 生效时间                | 目标          | 临时方案描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | 决策依据                    | 已知风险与局限       | 状态跟踪 | 债务等级 | 知识缺口 |
| ------------------- | ----------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------- | ------------- | ---- | ---- | ---- |
| 2025-07-10 09:24:44 | 定义目标制定的初始框架 | 使用“合格目标 = 核心价值 + 可衡量状态/可量化结果 + 时间线”结构描述目标                                                                                                                                                                                                                                                                                                                                                                                                                                                                | 通过结构化的处理方式可以简化目标制定的实现路径 | 1、细节不清晰，执行难度大 | 已失效  |      |      |
| 2025-07-10 18:10    | 补充执行框架的细节   | 1、思考目标的类型（行为驱动、流程改进、结果导向、学习成长）<br>（1）行为驱动：是否以“特定动作”的完成度作为目标达成的核心证据<br>（2）流程改进：是否以优化或重构“工作方式”本身为核心诉求<br>（3）结果导向：是否完全由可量化的终端业务成果或具体交付物定义<br>（4）学习成长：是否掌握可迁移的知识资产或能力凭证（如理解概念、通过认证、形成方法论）<br>2、选择合适的可衡量状态的量化方法<br>（1）行为驱动：将行为分解为最小可执行单元（如“健身”→深蹲/跑步）-->定义周期内执行次数（每日/每周X次）-->设定单次行为最低标准（时长/强度/产出物）-->指定可查验的行为证据<br>（2）流程改进：明确、具体、可核查的关键交付物<br>（3）结果导向：基线值 → 目标值（如“转化率从12%→18%”）/交付物必备要素清单<br>（4）学习成长：明确知识载体的内容要素/通过权威认证或场景化演示证明掌握<br>3、使用“合格目标 = 核心价值 + 可衡量状态/可量化结果 + 时间线”结构描述目标<br>4、详细内容见 [[效率工具箱#^002623]] |                         |               | 生效中  |      |      |
# 3. 根因分析

- 核心问题是什么？ --> 

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
