<%*
// 调试命令模板
// 功能：列出所有可用命令，帮助找到正确的 Tasks 命令

try {
  console.log("开始调试命令...");
  
  // 调用调试脚本
  const debugScript = tp.user["debug-commands"];
  if (typeof debugScript === 'function') {
    await debugScript(tp);
  } else {
    console.error("调试脚本未找到或不是函数");
    new Notice("❌ 调试脚本加载失败");
  }
  
} catch (error) {
  console.error("调用调试脚本时出错:", error);
  new Notice(`❌ 调试失败: ${error.message}`);
}

// 返回空字符串
return "";
%>
