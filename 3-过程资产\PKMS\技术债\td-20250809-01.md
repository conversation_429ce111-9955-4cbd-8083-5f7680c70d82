---
created_Date: 2025-08-09
aliases:
  - 技术债模板不可用
work_Item:
  - "[[TP-Project-技术债]]"
type: 系统债
priority: P1
status: 进行中
relation: 
cssclasses:
  - c3
---
# 1. 基础信息

| 序号  | 类型   | 场景描述          | 可观测现象                                       | 关键影响                        | 触发条件（如何发现？）                                                                  | 根本原因 |
| --- | ---- | ------------- | ------------------------------------------- | --------------------------- | ---------------------------------------------------------------------------- | ---- |
| 1   | 原始债务 | 根据模板记录技术债时    | 1、很难快速完成元数据字段的评估<br>2、基础信息填写顺序违背直觉，思维出现多次断裂 | 技术债记录中断，后续迭代无法正常开展（有计划偿还债务） | 1、判断技术债类型（`阻碍型/成本型/战略型/无害型`）、优先级（`高/中/低`）<br>2、记录基础信息（`发现场景、发现位置、根本原因、关键影响`） |      |
| 2   | 衍生债务 | 使用技术债模板制定偿还计划 | 1、切换其他文档时出现卡顿                               | 记录技术债时流畅度、体验感下降             | 使用自动化脚本自动同步偿还计划                                                              |      |
| 3   | 衍生债务 | 根据模板记录技术债时    | 1、模板内容不完整                                   | 技术债记录信息部分缺失，影响后续债务分析        | 使用DeepSeek验证模板逻辑、内容完整性时                                                      |      |
# 2. 应急方案
| 序号  | 日期         | 行动                        | 退出条件      |
| --- | ---------- | ------------------------- | --------- |
| 1   | 2025-08-09 | 停止记录技术债详细信息，仅简单填写`发生创景`信息 | 模板优化方案上线  |
| 2   | 2025-08-13 | 删除偿还计划表格格式，采用任务了格式手动创建任务  | 自动化系统重构完成 |
| 3   | 2025-08-14 | 兼容新发现与旧模板的内容              | 模板优化方案上线  |

# 3. 偿还计划

- [x] 重新设计模板`type`、`priority`字段 ➕ 2025-08-12 ✅ 2025-08-13
- [x] 重新设计模板正文部分 ➕ 2025-08-12 ✅ 2025-08-13
- [x] 利用[[td-20250809-01]]测试模板`type`、`priority`字段的合理性 ➕ 2025-08-12 ✅ 2025-08-13
- [x] 测试模板“基础信息”部分的逻辑合理性（[[td-20250809-01]]） ➕ 2025-08-13 ✅ 2025-08-13
- [x] 重新设计模板`position`、`stayus`字段 ➕ 2025-08-13 ✅ 2025-08-13
- [x] 梳理衍生债务的处理流程图 ➕ 2025-08-14 ✅ 2025-08-14
- [ ] 参考IOTO同步任务处理方式重构技术债任务创建脚本 ➕ 2025-08-15
- [ ] 根据测试的成功经验优化「[[TP-Project-技术债]]」 ➕ 2025-08-14
# 4. 验收清单

- [ ] 模板每个元数据字段评估时间≤3s
- [ ] 正文模块内容符合直觉流，且未频繁出现思维断裂