<%*
// 调试模态框模板
// 功能：调试任务窗口的检测和数据提取

try {
  console.log("开始调试模态框检测...");
  
  // 调用调试脚本
  const debugScript = tp.user["debug-modal"];
  if (typeof debugScript === 'function') {
    await debugScript(tp);
  } else {
    console.error("调试脚本未找到或不是函数");
    new Notice("❌ 调试脚本加载失败");
  }
  
} catch (error) {
  console.error("调用调试脚本时出错:", error);
  new Notice(`❌ 调试失败: ${error.message}`);
}

// 返回空字符串
return "";
%>
