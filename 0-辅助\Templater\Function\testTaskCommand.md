<%*
// 测试 Tasks 命令模板
// 功能：专门测试 Tasks 插件命令的执行

try {
  console.log("开始测试 Tasks 命令...");
  
  // 调用测试脚本
  const testScript = tp.user["test-task-command"];
  if (typeof testScript === 'function') {
    await testScript(tp);
  } else {
    console.error("测试脚本未找到或不是函数");
    new Notice("❌ 测试脚本加载失败");
  }
  
} catch (error) {
  console.error("调用测试脚本时出错:", error);
  new Notice(`❌ 测试失败: ${error.message}`);
}

// 返回空字符串
return "";
%>
