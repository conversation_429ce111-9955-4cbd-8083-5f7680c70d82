# 1.  成果验收
> [!dashboard]
> 
> > [!todo] 成果统计
> >```dataviewjs
> >// 获取当前笔记文件名并解析周数信息
> >const fileName = dv.current().file.name;
> >const weekMatch = fileName.match(/Replay-(\d{4})-WK(\d+)/);
> >if (!weekMatch) {
> >    dv.paragraph("**错误**：文件名格式不符合要求：Replay-YYYY-WKWW");
> >} else {
> >    const year = parseInt(weekMatch[1]);
> >    const currentWeek = parseInt(weekMatch[2]);
> >    const projectName = dv.current().file.path.split("/")[1];
> >    const dailyNotesPath = `2-项目/${projectName}/2-每日执行`;
> >    // 计算ISO周数的函数
> >    function getISOWeek(date) {
> >        const d = new Date(date);
> >        d.setHours(0,0,0,0);
> >        d.setDate(d.getDate() + 4 - (d.getDay() || 7));
> >        const yearStart = new Date(d.getFullYear(),0,1);
> >        return Math.ceil(((d - yearStart) / 86400000 + 1)/7);
> >    }
> >    // 存储所有条目
> >    const allEntries = [];
> >    let totalEntries = 0;
> >    // 获取每日执行笔记
> >    const dailyNotes = dv.pages(`"${dailyNotesPath}"`).file;
> >    // 筛选属于指定年份和周数的文件
> >    for (let file of dailyNotes) {
> >        const dateMatch = file.name.match(/Do-(\d{4})-(\d{2})-(\d{2})/);
> >        if (!dateMatch) continue;
> >		const dateStr = `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
> >		const fileDate = new Date(dateStr);
> >		if (isNaN(fileDate.getTime())) continue;
> >		// 检查是否匹配当前年份和周数
> >		if (fileDate.getFullYear() === year && 
> >			getISOWeek(fileDate) === currentWeek) {
> >			
> >			// 收集所有输出条目
> >			if (file.lists) {
> >				file.lists.forEach(list => {
> >					if (list.header?.subpath?.includes("输出") && 
> >						list.task === false && 
> >						list.text) {
> >						allEntries.push(list.text);
> >						totalEntries++;
> >					}});
> >			}
> >		}
> >    }
> >    // 输出结果
> >    if (totalEntries > 0) {
> >        // 按照条目名称排序
> >		allEntries.sort((a, b) => a.localeCompare(b, 'zh-CN'));
> >        const listContent = allEntries.map(entry => `- ${entry}`).join("\n");
> >        dv.paragraph(`本周共完成 ${totalEntries} 项输出\n${listContent}`);
> >    } else {
> >        dv.paragraph("本周没有找到任何输出条目");
> >    }
> >}
> >```
> 
> > [!tip] 任务统计
> >```dataviewjs
> >// 获取当前笔记文件名并构建规划文件名
> >const fileName = dv.current().file.name;
> >const planFileName = fileName.replace("Replay", "Plan") + ".md";
> >// 获取项目名称
> >const projectName = dv.current().file.path.split("/")[1];
> >// 规划文件路径
> >const planPath = `2-项目/${projectName}/1-每周计划`;
> >const planFile = dv.page(`${planPath}/${planFileName}`);
> >// 任务统计逻辑
> >if (planFile && planFile.file.tasks) {
> >    const allTasks = planFile.file.tasks;
> >    const totalTasks = allTasks.length;
> >    const completedTasks = allTasks.filter(t => t.checked);
> >    const pendingTasks = allTasks.filter(t => !t.checked);
> >    dv.el("p", `本周总计规划 ${totalTasks} 条任务，已完成 ${completedTasks.length} 条`);
> >    // 只显示未完成任务列表
> >    if (pendingTasks.length > 0) {
> >        dv.taskList(pendingTasks, false);
> >    }
> >} else {
> >    dv.paragraph("⚠️ 未找到对应的规划文件或任务数据");
> >}
> >```
# 2. KR进度

| 序号  | 成果（✅）                                      | `$= "[[2-项目/" + dv.current().file.path.split("/")[1] + "/" +dv.current().file.path.split("/")[1] + "-首页#1. OKR设定" + "\|关联KR]]"` | 价值描述                                | 风险预警 | 下一行动 |
| --- | :----------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------- | ---- | ---- |
| 1   | 「改善工作流程图」                                  | KR2：探索PKMS系统的核心工作流                                                                                                              | 1️⃣补全改善行动价值拼图                       |      |      |
| 2   | 1️⃣「阻碍优先级规则说明」<br>2️⃣「阻碍模板」<br>3️⃣「每周回顾」模板 | KR1：探索PKMS系统的基本内容结构（组件）                                                                                                         | 1️⃣明确阻碍优先级的排序规则，提升周回顾筛选高优先级阻碍的质量和效率 |      |      |
# 3. 本周阻碍
```dataviewjs
	// 获取当前文件名
	const currentFile = dv.current();
	const fileName = currentFile.file.name;
	// 从文件名中提取年份和周数
	const weekRegex = /Replay-(\d{4})-WK(\d{1,2})/;
	const match = fileName.match(weekRegex);
	if (!match) {
	    dv.paragraph("⚠️ 文件名格式不正确，应为 Replay-YYYY-WKWW");
	} else {
	    const [, year, weekNum] = match;
	    const projectName = currentFile.file.path.split("/")[1];
	    const path = `3-过程资产/${projectName}/阻碍`;
	    try {
	        // 计算指定周的时间范围
	        const baseDate = dv.date(`${year}-01-01`);
	        const weekStart = baseDate.plus({ days: (parseInt(weekNum) - 1) * 7 }).startOf('week');
	        const weekEnd = weekStart.plus({ days: 6 }); // 周日
	        // 格式化日期范围字符串
	        const dateRange = `${weekStart.toFormat("yyyy-MM-dd")} 至 ${weekEnd.toFormat("yyyy-MM-dd")}`;
	        // 创建日期解析函数
	        const parseDate = (value) => {
	            if (value?.isValid) return value; 
	            const dateStr = value?.toString() || "";
	            // 尝试解析多种日期格式
	            const formats = [
	                /(\d{4})-(\d{2})-(\d{2})/,             // YYYY-MM-DD
	                /(\d{1,2}) (\d{2}), (\d{4})/,          // M DD, YYYY
	                /(\d{1,2})\/(\d{1,2})\/(\d{4})/        // MM/DD/YYYY
	            ];
	            for (let format of formats) {
	                const match = dateStr.match(format);
	                if (match) {
	                    let [, part1, part2, year] = match;
	                    // 确定月份和日期
	                    const isDMY = format.source.includes('\\/') || format.source.includes(' ');
	                    const month = isDMY ? part1.padStart(2, '0') : part2.padStart(2, '0');
	                    const day = isDMY ? part2.padStart(2, '0') : part1.padStart(2, '0');
	                    return dv.date(`${year}-${month}-${day}`);
	                }
	            }
	            return dv.date(dateStr); // 最后尝试默认解析
	        };
	        // 获取本周创建的所有障碍
	        const weekFiles = dv.pages(`"${path}"`)
	            .filter(p => {
	                if (!p.file.name.match(/^blocker-\d{8}-\d{2}$/)) return false;
	                
	                const createdDate = parseDate(p.created_Date);
	                if (!createdDate?.isValid) return false;
	                
	                return createdDate >= weekStart && createdDate <= weekEnd;
	            })
	            .sort(p => p.created_Date, 'asc'); // 按创建日期排序
	        // 显示结果（优化输出部分）
	        if (weekFiles.length > 0) {
	            // 创建包含所有条目的 Markdown 字符串
	            const listContent = weekFiles.map(file => {
	                // 获取第一个别名（如果存在）
	                const alias = file.aliases?.length ? 
	                    (Array.isArray(file.aliases) ? file.aliases[0] : file.aliases) : 
	                    file.file.name;
	                // 处理状态
	                const status = file.status || "未设置";
	                // 创建列表项
	                return `- [[${file.file.path}|${alias}]] - 🚦${status}`;
	            }).join("\n");
	            // 使用单个输出语句
	            dv.paragraph(`本周新增 ${weekFiles.length} 条障碍 (${dateRange})\n${listContent}`);
	        } else {
	            dv.paragraph(`📭 第${weekNum}周 (${dateRange}) 未创建任何障碍记录`);
	        }
	    } catch (e) {
	        dv.paragraph(`⚠️ 处理错误: ${e.message}`);
	    }
	}
```
# 4. 交付异常

| 序号  | 成果/任务（🌗❌） | 交付状态 | 关联障碍 | 根因分析 | 下一步行动 |
| --- | ---------- | ---- | ---- | ---- | ----- |
|     |            |      |      |      |       |
