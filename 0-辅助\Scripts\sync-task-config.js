// 任务同步配置文件
// 功能：集中管理任务同步的配置选项

const SYNC_TASK_CONFIG = {
  // 目标文件配置
  targets: {
    // 默认目标
    default: {
      filePath: "Daily Notes/Tasks.md",
      section: "## Archived Tasks",
    },

    // 项目任务目标
    project: {
      filePath: "2-项目/{projectName}/任务管理.md",
      section: "## 同步任务",
    },

    // 每日任务目标
    daily: {
      filePath: "1-日常/{date}/任务.md",
      section: "## 今日任务",
    },
  },

  // 任务检测配置
  detection: {
    // 任务行正则表达式
    taskPattern: /^-\s*\[[\sx]\]\s+.+/,

    // 等待编辑完成的延迟时间（毫秒）
    editDelay: 500,

    // 监听超时时间（毫秒）
    listenTimeout: 30000,
  },

  // 通知配置
  notifications: {
    success: "✓ 任务已同步到 {targetFile}",
    error: "❌ {operation}失败: {message}",
    warning: "⚠️ {message}",
    timeout: "⚠️ 任务创建超时，已取消监听",
  },

  // 调试配置
  debug: {
    enabled: false,
    logPrefix: "[SyncTask]",
  },
};

// 获取配置的辅助函数
function getConfig(path = "") {
  if (!path) return SYNC_TASK_CONFIG;

  return path.split(".").reduce((obj, key) => {
    return obj && obj[key] !== undefined ? obj[key] : null;
  }, SYNC_TASK_CONFIG);
}

// 格式化消息模板
function formatMessage(template, variables = {}) {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return variables[key] || match;
  });
}

// 获取目标配置（支持动态变量替换）
function getTargetConfig(targetType = "default", variables = {}) {
  const config = getConfig(`targets.${targetType}`);
  if (!config) return getConfig("targets.default");

  return {
    filePath: formatMessage(config.filePath, variables),
    section: formatMessage(config.section, variables),
  };
}

// 调试日志函数
function debugLog(message, data = null) {
  if (getConfig("debug.enabled")) {
    const prefix = getConfig("debug.logPrefix");
    console.log(`${prefix} ${message}`, data || "");
  }
}

// Templater 兼容的导出方式
module.exports = function () {
  // 在 Obsidian 环境中，将配置添加到全局对象
  if (typeof window !== "undefined") {
    window.SyncTaskConfig = {
      SYNC_TASK_CONFIG,
      getConfig,
      formatMessage,
      getTargetConfig,
      debugLog,
    };
  }

  // 返回配置对象供直接使用
  return {
    SYNC_TASK_CONFIG,
    getConfig,
    formatMessage,
    getTargetConfig,
    debugLog,
  };
};
