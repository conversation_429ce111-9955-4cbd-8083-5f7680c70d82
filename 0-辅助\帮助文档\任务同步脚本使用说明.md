# 任务同步脚本使用说明

## 📋 功能概述

任务同步脚本 (`sync-task.js`) 是一个 Obsidian 自动化工具，用于：
- 打开 Tasks 插件的任务创建窗口
- 自动捕获新创建的任务
- 将任务复制到指定的目标文件
- 从原始位置清理临时任务

## 🔧 脚本组成

### 核心文件
- `sync-task.js` - 主要同步逻辑
- `sync-task-caller.js` - 简化调用接口
- `sync-task-config.js` - 配置管理
- `syncTask.md` - Templater 调用模板

### 配置文件
- `任务同步脚本使用说明.md` - 本说明文档

## 🚀 使用方法

### 方法一：通过 Templater 模板调用

1. **配置 Templater 快捷键**
   - 打开 Templater 设置
   - 在 "Hotkeys" 部分添加 `syncTask.md` 模板
   - 设置快捷键（如 `Ctrl+Shift+T`）

2. **使用步骤**
   - 在任意文件中按快捷键
   - 系统会自动打开任务创建窗口
   - 创建任务后，任务会自动同步到目标文件

### 方法二：通过脚本直接调用

1. **在其他脚本中调用**
```javascript
const syncTask = require('./sync-task-caller.js');
await syncTask(tp);
```

## ⚙️ 配置选项

### 基本配置

在 `sync-task.js` 中修改以下变量：

```javascript
const targetFilePath = "Daily Notes/Tasks.md"; // 目标文件路径
const targetSection = "## Archived Tasks";    // 目标章节标题
```

### 高级配置

使用 `sync-task-config.js` 进行更灵活的配置：

```javascript
// 多目标配置
targets: {
  default: {
    filePath: "Daily Notes/Tasks.md",
    section: "## Archived Tasks"
  },
  project: {
    filePath: "2-项目/{projectName}/任务管理.md",
    section: "## 同步任务"
  }
}
```

## 🔍 主要改进

### 原始问题修复

1. **事件监听器泄漏** ✅
   - 添加了超时清理机制（30秒）
   - 异常情况下自动清理监听器

2. **差异检测算法** ✅
   - 支持多行任务检测
   - 使用正则表达式验证任务格式
   - 区分新增和修改的任务

3. **错误处理** ✅
   - 完整的 try-catch 错误处理
   - 详细的错误日志和用户通知
   - 资源清理保证

4. **插入位置逻辑** ✅
   - 精确的标题定位算法
   - 正确的换行符处理
   - 避免破坏现有格式

5. **重复任务检测** ✅
   - 检查任务是否已存在
   - 避免重复添加相同任务

## 📁 文件结构

```
0-辅助/
├── Scripts/
│   ├── sync-task.js              # 主同步脚本（已修复）
│   ├── sync-task-caller.js       # 调用器脚本
│   └── sync-task-config.js       # 配置管理
├── Templater/
│   └── Function/
│       └── syncTask.md           # Templater 调用模板
└── 帮助文档/
    └── 任务同步脚本使用说明.md    # 本说明文档
```

## 🎯 工作流程

```mermaid
graph TD
    A[用户触发脚本] --> B[检查 Tasks 插件]
    B --> C[检查目标文件]
    C --> D[保存原始内容]
    D --> E[设置监听器]
    E --> F[打开任务创建窗口]
    F --> G[用户创建任务]
    G --> H[检测内容变化]
    H --> I{发现新任务?}
    I -->|是| J[同步到目标文件]
    I -->|否| K[继续监听]
    J --> L[恢复原始内容]
    L --> M[清理监听器]
    M --> N[显示成功通知]
    K --> O{超时?}
    O -->|是| P[清理监听器]
    O -->|否| H
```

## ⚠️ 注意事项

### 依赖要求
- 必须安装并启用 `obsidian-tasks-plugin`
- 需要 Templater 插件（如使用模板调用）

### 使用限制
- 目标文件必须存在
- 只能检测标准格式的任务行
- 监听器有30秒超时限制

### 最佳实践
1. 在使用前确保目标文件已创建
2. 建议使用标准的任务格式：`- [ ] 任务内容`
3. 避免在任务创建过程中进行其他编辑操作

## 🐛 故障排除

### 常见问题

1. **任务没有同步**
   - 检查 Tasks 插件是否启用
   - 确认目标文件路径正确
   - 查看浏览器控制台错误信息

2. **监听器超时**
   - 任务创建时间过长（超过30秒）
   - 可以重新执行脚本

3. **重复任务**
   - 脚本会自动检测并跳过重复任务
   - 如需强制添加，可手动修改任务内容

### 调试模式

在 `sync-task-config.js` 中启用调试：

```javascript
debug: {
  enabled: true,
  logPrefix: "[SyncTask]"
}
```

## 📈 未来改进

- [ ] 支持批量任务同步
- [ ] 添加任务分类和标签
- [ ] 支持自定义任务模板
- [ ] 集成项目管理工作流
