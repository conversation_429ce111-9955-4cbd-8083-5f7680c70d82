// 调试命令脚本
// 功能：列出所有可用的命令，特别是 Tasks 相关的命令

module.exports = async function (tp) {
  try {
    console.log("=== 开始调试命令 ===");
    
    // 获取所有命令
    const allCommands = app.commands.listCommands();
    console.log(`总共找到 ${allCommands.length} 个命令`);
    
    // 过滤 Tasks 相关命令
    const taskCommands = allCommands.filter(cmd => 
      cmd.id.toLowerCase().includes("task") || 
      cmd.name.toLowerCase().includes("task") ||
      cmd.id.includes("obsidian-tasks-plugin") ||
      cmd.name.toLowerCase().includes("create") ||
      cmd.name.toLowerCase().includes("edit")
    );
    
    console.log("\n=== Tasks 和创建相关的命令 ===");
    taskCommands.forEach((cmd, index) => {
      console.log(`${index + 1}. ID: "${cmd.id}"`);
      console.log(`   Name: "${cmd.name}"`);
      console.log(`   Callback: ${typeof cmd.callback}`);
      console.log("---");
    });
    
    // 特别查找包含 "Tasks" 的命令
    const tasksSpecific = allCommands.filter(cmd => 
      cmd.name.includes("Tasks") || cmd.id.includes("tasks-plugin")
    );
    
    console.log("\n=== 专门的 Tasks 插件命令 ===");
    tasksSpecific.forEach((cmd, index) => {
      console.log(`${index + 1}. ID: "${cmd.id}"`);
      console.log(`   Name: "${cmd.name}"`);
      console.log("---");
    });
    
    // 检查 Tasks 插件对象
    const tasksPlugin = app.plugins.getPlugin("obsidian-tasks-plugin");
    if (tasksPlugin) {
      console.log("\n=== Tasks 插件对象信息 ===");
      console.log("插件已加载:", tasksPlugin._loaded);
      console.log("插件方法:", Object.getOwnPropertyNames(tasksPlugin));
      
      // 检查插件的原型方法
      const proto = Object.getPrototypeOf(tasksPlugin);
      console.log("原型方法:", Object.getOwnPropertyNames(proto));
    }
    
    // 显示结果给用户
    new Notice(`找到 ${taskCommands.length} 个相关命令，请查看控制台`, 5000);
    
    return null;
    
  } catch (error) {
    console.error("调试命令脚本出错:", error);
    new Notice(`❌ 调试失败: ${error.message}`);
    return null;
  }
};
