<%*
// 测试智能任务同步模板
// 功能：测试新的智能任务同步逻辑

try {
  console.log("开始测试智能任务同步...");
  
  // 调用测试脚本
  const testScript = tp.user["test-smart-sync"];
  if (typeof testScript === 'function') {
    await testScript(tp);
  } else {
    console.error("测试脚本未找到或不是函数");
    new Notice("❌ 测试脚本加载失败");
  }
  
} catch (error) {
  console.error("调用测试脚本时出错:", error);
  new Notice(`❌ 测试失败: ${error.message}`);
}

// 返回空字符串
return "";
%>
