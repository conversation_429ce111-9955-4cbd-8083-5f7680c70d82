---
homePageLink: "[[PKMS-首页]]"
---
# 1. 改善回顾

```dataviewjs
	try {
	    // 汇总表格数据到当前页面
	    const projectName = dv.current().file.path.split("/")[1].trim();
	    const targetFolder = `2-项目/${projectName}/4-每周回顾`;
	    const tableTitlePattern = "流程改善"; // 标题中包含的关键词
	
	    // 1. 从当前文件名确定周数
	    const currentFileName = dv.current().file.name;
	    const weekMatch = currentFileName.match(/^Review-(\d{4})-WK(\d{2})$/);
	    if (!weekMatch) {
	        dv.el("p", "⚠️ 当前文件名格式不正确，应为 Review-YYYY-WKxx");
	        return;
	    }
	    
	    const targetYear = parseInt(weekMatch[1]);
	    const targetWeek = parseInt(weekMatch[2]);	
	    let allTableData = [];
	    let filesProcessed = 0;
	    let tablesFound = 0;
	    let skippedFiles = 0;
	
	    // 2. 处理小于目标周数的所有Review文件
	    for (let file of dv.pages(`"${targetFolder}"`).file) {
	        const fileMatch = file.name.match(/^Review-(\d{4})-WK(\d{2})$/);
	        if (!fileMatch) {
	            skippedFiles++;
	            continue;
	        }
	        const fileYear = parseInt(fileMatch[1]);
	        const fileWeek = parseInt(fileMatch[2]);
	
	        // 仅处理周数严格小于当前的文件
	        if (fileYear < targetYear || 
	           (fileYear === targetYear && fileWeek < targetWeek)) {
	            
	            filesProcessed++;
	            const content = await dv.io.load(file.path);
	            
	            // 识别指定标题下的内容区域
	            const headingRegex = new RegExp(
	                `(?:^|\\n)#+\\s*.*${tableTitlePattern}.*[^\\n]*\\n([\\s\\S]*?)(?=\\n#|$)`, 
	                "i"
	            );
	            
	            const match = content.match(headingRegex);
	            if (!match || !match[1]) continue;
	            
	            const sectionContent = match[1].trim();
	            
	            // 提取表格数据
	            const tableRegex = /^\s*\|(.+)\|\n\s*\|?\s*:?-+:?\s*\|.+\n((?:\s*\|.+\|\n?)+)/gms;
	            let tableMatch;
	            
	            while ((tableMatch = tableRegex.exec(sectionContent)) !== null) {
	                const headerRow = tableMatch[1].split('|')
	                    .map(cell => cell.trim()).filter(Boolean);
	                const dataRows = tableMatch[2].split('\n')
	                    .filter(row => row.includes('|') && !row.startsWith('|-'))
	                    .map(row => 
	                        row.split('|')
	                        .slice(1, -1)
	                        .map(cell => cell.trim())
	                    );
	                
	                if (headerRow.length > 1 && dataRows.length > 0) {
	                    // 创建文件链接（关键修改点）
	                    const weekTag = `${fileYear}-WK${fileWeek.toString().padStart(2,'0')}`;
	                    const fileName = `Review-${weekTag}.md`;
	                    const fileLink = dv.fileLink(`${targetFolder}/${fileName}`, false, weekTag);
	                    
	                    // 添加表头（首次识别时）
	                    if (allTableData.length === 0) {
	                        allTableData.push(["周", ...headerRow]);
	                    }
	                    
	                    // 添加数据行（使用文件链接对象）
	                    dataRows.forEach(row => {
	                        allTableData.push([fileLink, ...row]);
	                    });
	                    
	                    tablesFound++;
	                }
	            }
	        } else {
	            skippedFiles++;
	        }
	    }	    
	    if (allTableData.length > 0) {
	        dv.table(allTableData[0], allTableData.slice(1));
	    }else{
		    dv.el("p", "无数据");
	    }
	} catch(error) {
	    dv.el("p", `❌ 处理错误: ${error.message}`);
	    console.error(error);
	}
```

# 2. 流程改善

| 对象  | 类型  | 关联工作流环节 | 根因分析 | 改进措施 | 验证指标 | 状态  | 完成日期 |
| --- | --- | ------- | ---- | ---- | ---- | --- | ---- |
|     |     |         |      |      |      |     |      |

# 3. 成功经验

| 超预期成果 | 根因分析 | 关键行为 | 证据链 | 经验封装 | 复用场景 |
| ----- | ---- | ---- | --- | ---- | ---- |
|       |      |      |     |      |      |
