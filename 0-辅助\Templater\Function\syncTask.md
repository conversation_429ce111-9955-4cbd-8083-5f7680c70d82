<%*
// 任务同步功能调用模板
// 功能：调用 sync-task.js 脚本，实现任务创建和同步

try {
  // 获取脚本路径
  const scriptPath = "0-辅助/Scripts/sync-task.js";
  
  // 检查脚本文件是否存在
  const scriptFile = app.vault.getAbstractFileByPath(scriptPath);
  if (!scriptFile) {
    new Notice(`❌ 脚本文件不存在: ${scriptPath}`);
    return "";
  }

  // 获取当前活动文件
  const activeFile = app.workspace.getActiveFile();
  if (!activeFile) {
    new Notice("❌ 请先打开一个文件");
    return "";
  }

  // 动态加载并执行脚本
  const scriptContent = await app.vault.read(scriptFile);
  const scriptFunction = eval(`(${scriptContent})`);
  
  // 创建执行上下文
  const context = {
    app: {
      app: app,
      moment: moment,
      file: activeFile,
      workspace: app.workspace
    }
  };

  // 执行脚本
  await scriptFunction.call(context, {});
  
} catch (error) {
  console.error("执行任务同步脚本时出错:", error);
  new Notice(`❌ 执行失败: ${error.message}`);
}

// 返回空字符串，不在文档中插入内容
return "";
%>
