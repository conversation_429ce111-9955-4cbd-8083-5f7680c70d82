{"presets": {}, "globalQuery": "", "globalFilter": "", "removeGlobalFilter": false, "taskFormat": "tasksPluginEmoji", "setCreatedDate": false, "setDoneDate": true, "setCancelledDate": false, "autoSuggestInEditor": false, "autoSuggestMinMatch": 1, "autoSuggestMaxItems": 20, "provideAccessKeys": true, "useFilenameAsScheduledDate": false, "filenameAsScheduledDateFormat": "", "filenameAsDateFolders": [], "recurrenceOnNextLine": true, "removeScheduledDateOnRecurrence": false, "statusSettings": {"coreStatuses": [{"symbol": " ", "name": "Todo", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "TODO"}, {"symbol": "x", "name": "Done", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "DONE"}], "customStatuses": [{"symbol": "-", "name": "Cancelled", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "CANCELLED"}]}, "features": {"INTERNAL_TESTING_ENABLED_BY_DEFAULT": true}, "generalSettings": {}, "headingOpened": {"Core Statuses": true, "Custom Statuses": true, "核心状态": true, "自定义状态": true}, "debugSettings": {"ignoreSortInstructions": false, "showTaskHiddenData": false, "recordTimings": false}, "loggingOptions": {"minLevels": {"": "info", "tasks": "info", "tasks.Cache": "info", "tasks.Events": "info", "tasks.File": "info", "tasks.Query": "info", "tasks.Task": "info"}}}