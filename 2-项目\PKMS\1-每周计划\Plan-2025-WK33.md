# 1. 周目标

- 优化技术债模板

# 2. 验收标准

| 序号  | 交付物                      | 标准/通过条件                                           | 备注  |
| --- | ------------------------ | ------------------------------------------------- | --- |
| 1   | 「[[TP-Project-技术债]]」V1.1 | 1、基础信息仅包含技术债的核心部分<br>2、基础信息从上到下必须符合认知逻辑且有利于制定偿还计划 |     |
| 2   | 「[[技术债模板使用说明]]」V1.1      | 1、说明模板元数据字段的定义及填写依据<br>2、说明模板正文每个模块的核心作用及填写要点     |     |
# 3. 改进事项
```tasks
limit 5
heading includes 行动
description regex matches /\S/
hide backlink
is not blocked
priority is not none
sort by priority
filter by function \
const projectName = query.file.path.split("/")[1]; \
const targetPath = `2-项目/${projectName}/改进待办事项`; \
const pathMatches = task.file.path.includes(targetPath);\
return pathMatches;
```

# 4. 技术债
```dataviewjs
	// 统一配置：类型和优先级设置
	const TECH_DEBT_CONFIG = {
	    types: {
	        presets: ["阻塞型", "成本型", "战略型", "无害型"],
	        get set() { return new Set(this.presets); }
	    },
	    priorities: {
	        presets: ["立即", "高", "中", "低"],
	        get set() { return new Set(this.presets); },
	        get order() { 
	            const order = {};
	            this.presets.forEach((p, i) => order[p] = i + 1);
	            return order;
	        }
	    }
	};
	
	// 获取技术债文件
	const projectName = dv.current().file.path.split("/")[1];
	const folderPath = `3-过程资产/${projectName}/技术债`;
	const allTechDebtFiles = dv.pages(`"${folderPath}"`)
	    .filter(p => 
	        p.file.name.match(/^td-\d{8}-\d{2}$/) && 
	        p.status !== "已解决" &&
	        p.file.name !== "dashboard"
	    );
	
	// 检测非预设值文件
	const hasInvalidValue = (file) => {
	    const type = file.type?.toString();
	    const priority = file.priority?.toString();
	    
	    return !TECH_DEBT_CONFIG.types.set.has(type) || 
	           !TECH_DEBT_CONFIG.priorities.set.has(priority);
	};
	
	const invalidFiles = allTechDebtFiles.filter(hasInvalidValue);
	
	// 辅助函数：获取类型显示值（仅显示预设值或提示信息）
	const getTypeDisplay = (file) => {
	    const type = file.type?.toString();
	    if (!type) return "(未设置类型)";
	    return TECH_DEBT_CONFIG.types.set.has(type) ? type : "(无效类型)";
	};
	
	// 辅助函数：获取优先级显示值（仅显示预设值或提示信息）
	const getPriorityDisplay = (file) => {
	    const priority = file.priority?.toString();
	    if (!priority) return "(未设置优先级)";
	    return TECH_DEBT_CONFIG.priorities.set.has(priority) ? priority : "(无效优先级)";
	};
	
	// 显示结果
	if (invalidFiles.length > 0) {
	    // 输出非预设值文件  
	    const listItems = invalidFiles.map(file => {
	        const displayName = file.aliases || file.file.name;
	        const typeDisplay = getTypeDisplay(file);
	        const priorityDisplay = getPriorityDisplay(file);
	        return `[[${file.file.name}|${displayName}]] - ${typeDisplay} - ${priorityDisplay}`;
	    });
	    
	    // 使用单个Markdown列表输出
	    dv.paragraph(listItems);
	} else {
	    // 如果没有非预设值文件，则按原逻辑处理预设值文件
	    const validFiles = allTechDebtFiles;
	    // 确定目标类型
	    let targetType = null;
	    for (const type of TECH_DEBT_CONFIG.types.presets) {
	        if (validFiles.some(p => p.type === type)) {
	            targetType = type;
	            break;
	        }
	    }
	    if (!targetType) {
	        dv.paragraph("⚠️ 没有待处理的技术债");
	    } else {
	        // 过滤并排序文件
	        const filteredFiles = validFiles
	            .filter(p => p.type === targetType)
	            .sort(p => TECH_DEBT_CONFIG.priorities.order[p.Priority] || 999);
	        
	        if (filteredFiles.length === 0) {
	            dv.paragraph(`⚠️ 没有${targetType}类型的技术债`);
	        } else {          
	            // 创建优先级分组
	            const groupedByPriority = {};
	            filteredFiles.forEach(file => {
	                const priority = file.priority || "未设置";
	                if (!groupedByPriority[priority]) {
	                    groupedByPriority[priority] = [];
	                }
	                groupedByPriority[priority].push(file);
	            });
	            
	            // 收集所有输出行
	            const outputLines = [];
	            TECH_DEBT_CONFIG.priorities.presets.forEach(priority => {
	                if (groupedByPriority[priority]) {
	                    groupedByPriority[priority].forEach(file => {
	                        const displayName = file.aliases || file.file.name;
	                        outputLines.push(
	                            `[[${file.file.name}|${displayName}]] - ${file.type} - ${priority}`
	                        );
	                    });
	                }
	            });
	            // 使用单个Markdown列表输出所有内容
	            dv.paragraph(outputLines);
	        }
	    }
	}
```

# 5. 任务拆解
- [ ] 模板每个元数据字段评估时间≤3s
- [ ] 正文模块内容符合直觉流，且未频繁出现思维断裂

- [x] 重新设计模板`type`、`priority`字段 ➕ 2025-08-12 ⏳ 2025-08-11 ✅ 2025-08-13
- [x] 重新设计模板正文部分 ➕ 2025-08-12 ⏳ 2025-08-11 ✅ 2025-08-13
- [x] 利用[[td-20250809-01]]测试模板`type`、`priority`字段的合理性 ➕ 2025-08-12 ⏳ 2025-08-11 ✅ 2025-08-13
- [x] 测试模板“基础信息”部分的逻辑合理性（[[td-20250809-01]]） ➕ 2025-08-13 ⏳ 2025-08-11 ✅ 2025-08-13
- [x] 重新设计模板`position`、`stayus`字段 ➕ 2025-08-13 ⏳ 2025-08-14 ✅ 2025-08-13
- [x] 梳理衍生债务的处理流程图 ➕ 2025-08-14 ⏳ 2025-08-14 ✅ 2025-08-14
- [ ] 根据测试的成功经验优化「[[TP-Project-技术债]]」 ➕ 2025-08-12 ⏳ 2025-08-14