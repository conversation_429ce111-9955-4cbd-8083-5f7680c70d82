// 简化任务同步脚本
// 功能：监听当前文件的任务创建，自动同步到目标文件

module.exports = async function (tp) {
  try {
    console.log("开始执行简化任务同步...");
    
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("❌ 请先打开一个文件");
      return null;
    }

    // 配置目标文件
    const targetFilePath = "2-项目/PKMS/1-每周计划/Plan-2025-WK33.md";
    const targetSection = "# 5. 任务拆解";

    // 检查目标文件
    const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    if (!targetFile) {
      new Notice(`❌ 目标文件不存在: ${targetFilePath}`);
      return null;
    }

    // 获取原始内容
    const originalContent = await app.vault.read(activeFile);
    console.log("已保存原始内容");

    // 显示指导消息
    new Notice("✓ 任务同步已激活！请在当前文件中创建任务，将自动同步到目标文件", 8000);
    
    // 设置监听状态
    let isListening = true;
    let changeTimeout = null;

    // 监听器函数
    const handleChange = async () => {
      if (!isListening) return;
      
      console.log("检测到文件变化");
      clearTimeout(changeTimeout);
      
      changeTimeout = setTimeout(async () => {
        try {
          const newContent = await app.vault.read(activeFile);
          
          if (newContent !== originalContent) {
            console.log("内容确实发生了变化");
            
            // 查找新增的任务
            const newTasks = findNewTasks(originalContent, newContent);
            
            if (newTasks.length > 0) {
              console.log("找到新任务:", newTasks);
              
              // 停止监听
              isListening = false;
              app.workspace.off("editor-change", handleChange);
              
              // 同步任务
              const success = await syncTasksToTarget(newTasks, targetFilePath, targetSection);
              
              if (success) {
                // 恢复原始内容（移除临时任务）
                await app.vault.modify(activeFile, originalContent);
                new Notice(`✅ 成功同步 ${newTasks.length} 个任务！`);
                console.log("任务同步完成");
              } else {
                new Notice("❌ 任务同步失败，请检查控制台");
              }
            } else {
              console.log("未检测到新任务");
            }
          }
        } catch (error) {
          console.error("处理文件变化时出错:", error);
          new Notice(`❌ 处理失败: ${error.message}`);
        }
      }, 1000); // 1秒延迟
    };

    // 设置超时清理（60秒）
    setTimeout(() => {
      if (isListening) {
        isListening = false;
        app.workspace.off("editor-change", handleChange);
        new Notice("⏰ 任务同步监听已超时停止");
        console.log("监听超时停止");
      }
    }, 60000);

    // 开始监听
    app.workspace.on("editor-change", handleChange);
    console.log("开始监听文件变化");

    return null;

  } catch (error) {
    console.error("简化任务同步脚本出错:", error);
    new Notice(`❌ 脚本执行失败: ${error.message}`);
    return null;
  }
};

// 查找新增的任务
function findNewTasks(originalContent, newContent) {
  const originalLines = originalContent.split('\n');
  const newLines = newContent.split('\n');
  const tasks = [];

  // 检查每一行
  for (let i = 0; i < newLines.length; i++) {
    const newLine = newLines[i];
    const originalLine = i < originalLines.length ? originalLines[i] : '';
    
    // 如果这一行发生了变化
    if (newLine !== originalLine) {
      const trimmedLine = newLine.trim();
      
      // 检查是否是任务格式
      if (trimmedLine.match(/^-\s*\[[\sx]\]\s+.+/)) {
        tasks.push(trimmedLine);
        console.log("检测到任务行:", trimmedLine);
      }
    }
  }

  return tasks;
}

// 同步任务到目标文件
async function syncTasksToTarget(tasks, targetFilePath, targetSection) {
  try {
    const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    let fileContent = await app.vault.read(targetFile);
    
    const tasksText = tasks.join('\n');
    console.log("准备同步的任务:", tasksText);
    
    // 查找目标章节
    const sectionIndex = fileContent.indexOf(targetSection);
    
    if (sectionIndex === -1) {
      // 如果章节不存在，添加到文件末尾
      fileContent += `\n\n${targetSection}\n${tasksText}\n`;
      console.log("章节不存在，添加到文件末尾");
    } else {
      // 在章节下方插入任务
      const headerEndIndex = sectionIndex + targetSection.length;
      const nextLineIndex = fileContent.indexOf('\n', headerEndIndex);
      
      if (nextLineIndex === -1) {
        fileContent += `\n${tasksText}\n`;
      } else {
        fileContent = 
          fileContent.substring(0, nextLineIndex + 1) +
          `${tasksText}\n` +
          fileContent.substring(nextLineIndex + 1);
      }
      console.log("任务已插入到章节下方");
    }
    
    // 保存文件
    await app.vault.modify(targetFile, fileContent);
    console.log("目标文件已更新");
    
    return true;
    
  } catch (error) {
    console.error("同步任务到目标文件时出错:", error);
    return false;
  }
}
