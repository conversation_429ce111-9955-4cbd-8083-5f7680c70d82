---
aliases:
  - 周回顾阻碍优先级规则失效
created_Date: 2025-07-19
status: 待验证
relation:
  - "[[Review-2025-WK29]]"
  - "[[Review-2025-WK30]]"
cssclasses:
  - c3
type: 价值威胁
---
# 1. 基础信息

- 可观测现象：
	- 看板查询结果条目较多
	- 看板TOP3结果与实际不符
- 触发条件：
	- 使用`分组+频率`规则进行阻碍筛选
	- 使用 $损耗值^2*频率/解决成本$ 规则进行阻碍筛选
- 影响：
	- 无法明确周回顾的具体切入口
	- 周回顾工作中断
- 背景描述：
	- 29周回顾阶段，查看看板数据时
	- 30周回顾阶段，查看看板数据时

# 2. 临时方案

| 生效时间                | 目标        | 临时方案描述                                                                                | 决策依据                                           | 已知风险与局限                             | 状态跟踪 | 债务等级 | 知识缺口 |
| ------------------- | --------- | ------------------------------------------------------------------------------------- | ---------------------------------------------- | ----------------------------------- | ---- | ---- | ---- |
| 2025-07-19 10:15:03 | 快速定位高价值阻碍 | 1、为每个阻碍添加临时的“优先级”字段<br>2、整体评估阻碍的严重程度，用1-10分进行评估，分值越大优先级越高<br>2、创建临时查询代码，按照“优先级”字段进行排序 | 凭感觉已经无法进行排序，根据最新标准简化模型可以降低操作难度，节省时间，也兼容了一定的合理性 | 1、增加了后续阻碍标准化的执行难度<br>2、判断依据粗糙，准确度不高 | 生效中  | ★    |      |
# 3. 根因分析

- 筛选规则为什么会失效？ --> 将规则交付当作一次性任务
- 为什么会如此行动？-->规则管理缺乏迭代机制

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
