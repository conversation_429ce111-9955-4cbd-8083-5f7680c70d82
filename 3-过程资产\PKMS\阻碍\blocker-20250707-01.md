---
aliases:
  - 阻碍别名识别困难
created_Date: 2025-07-07
status: 待验证
relation:
  - "[[Review-2025-WK26]]"
  - "[[Review-2025-WK27]]"
  - "[[Review-2025-WK28]]"
  - "[[Review-2025-WK29]]"
cssclasses:
  - c3
type: 阻塞型
---
# 1. 基础信息

- 现象描述：通过阻碍的别名识别阻碍时，很难快速理解其背后的具体问题是什么
- 直接影响：降低了查看阻碍的效率
- 衍生影响：导致”每周回顾“环节识别和解决阻碍的难度增大，影响了整个阶段的工作效率

# 2. 临时方案

| 生效时间                | 目标           | 临时方案描述                                                                                                                                                                                                                                                                           | 决策依据                                                    | 已知风险与局限                                             | 状态跟踪 | 债务等级 | 知识缺口 |
| ------------------- | ------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------- | --------------------------------------------------- | ---- | ---- | ---- |
| 2025-07-07 17:53    | 使用固定句式描述阻碍   | 使用“发生了什么问题”（现象），并明确指出“这造成了什么后果（影响）”句式描述阻碍                                                                                                                                                                                                                                        | 降低了认知负担，结构化模板有利于思考                                      |                                                     | 已失效  |      |      |
| 2025-07-08 10:28:10 | 明确句式的使用场景和特例 | 1、使用”由于 「具体现象」，导致「直接后果」，进而造成「深层影响」“结构描述阻碍<br>2、「具体现象」-->主谓短语（隐性主语+谓语）/定中结构名词短语<br>3、「直接后果」-->主谓宾结构（显性主语）<br>4、「深层影响」-->定中结构名词短语（定语+中心词）<br>5、主谓宾结构拓展<br>（1）动词前不加状语场景---动作场景已隐含、主谓宾可独立表意、状语重复修饰<br>（2）动词 + 补语-->说明动作的结果、程度、状态、可能性等，形容词 + 补语-->说明性质的程度或比较基准<br>（3）被动式主谓结构（无灵主语+动词） | 通过明确「直接后果」的语法结构，简化合理描述的判断标准                             | 1、标准对应的场景较多，增加了执行难度                                 | 已失效  |      |      |
| 2025-07-10 16:46:26 | 降低描述阻碍的认知负担  | 1、使用结构化框架描述阻碍<br>（1）别名：简洁描述阻碍核心特征<br>（2）基础信息：现象描述+直接影响+衍生影响<br>2、将别名当做检索标签，基础信息当作事实记录                                                                                                                                                                                            | 1、针对性的解决索引和识别的痛点<br>2、在保证检索的同时，保留了阻碍的特定上下文信息，已便于定位、解决问题 | 1、基础信息仅保留了阻碍的最基础、最核心的信息，缺少发生环节/场景信息，可能不利于后续阻碍的重复性判断 | 生效中  |      |      |
# 3. 根因分析

- 核心问题是什么？ --> 阻碍别名难以被快速识别？
- 为什么无法被快速识别？ --> 设置别名的目的便于索引，不是准确描述阻碍
- 为什么会混淆两者？ --> 项目初期，阻碍被记录在「每日执行」中，更强调用一句话描述阻碍。经过多次迭代，发现阻碍需要被集中化管理，所以直接将阻碍描述移动至“别名”属性
- 为什么会简单平移？ --> 历史路径依赖，掉入了认知惯性陷阱（形成思维定式）

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
