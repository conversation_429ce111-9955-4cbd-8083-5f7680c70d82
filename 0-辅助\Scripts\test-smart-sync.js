// 测试智能任务同步脚本
// 功能：测试新的智能任务同步逻辑

module.exports = async function (tp) {
  try {
    console.log("=== 开始测试智能任务同步 ===");
    
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("❌ 请先打开一个文件");
      return null;
    }

    console.log("当前文件:", activeFile.path);

    // 测试项目信息提取
    const projectInfo = getProjectInfoFromTechDebtFile(activeFile);
    console.log("项目信息:", projectInfo);

    if (!projectInfo) {
      new Notice("❌ 当前文件不是技术债文件");
      console.log("文件路径不匹配技术债模式");
      return null;
    }

    // 测试目标文件路径生成
    const targetFilePath = generateTargetFilePath(projectInfo.projectName);
    console.log("生成的目标文件路径:", targetFilePath);

    // 检查目标文件是否存在
    const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    if (!targetFile) {
      new Notice(`❌ 目标文件不存在: ${targetFilePath}`);
      console.log("目标文件不存在，请先创建");
      return null;
    }

    console.log("✅ 目标文件存在");

    // 启动任务窗口监听
    console.log("开始监听任务窗口...");
    new Notice("✅ 智能任务同步已启动，请打开 Tasks 创建窗口", 5000);

    // 创建监听器
    let isListening = true;
    let previousTaskData = null;

    const windowListener = async () => {
      if (!isListening) return;

      try {
        // 查找模态框
        const modals = document.querySelectorAll('.modal, [class*="modal"], [class*="dialog"]');
        
        if (modals.length === 0) return;

        console.log(`检测到 ${modals.length} 个模态框`);

        for (const modal of modals) {
          const modalContent = modal.textContent || '';
          const hasInputs = modal.querySelector('input, textarea');
          
          if (hasInputs && (modalContent.includes('Task') || modalContent.includes('Description'))) {
            console.log("找到任务相关的模态框");
            
            // 提取任务数据
            const currentTaskData = extractTaskDataFromModal(modal);
            console.log("当前任务数据:", currentTaskData);

            if (currentTaskData && hasTaskDataChanged(previousTaskData, currentTaskData)) {
              console.log("检测到任务数据变化");
              
              // 查找 Apply 按钮
              const applyButtons = modal.querySelectorAll('button');
              console.log(`找到 ${applyButtons.length} 个按钮`);
              
              applyButtons.forEach((btn, index) => {
                console.log(`按钮 ${index + 1}: "${btn.textContent}" (${btn.className})`);
              });

              // 监听按钮点击
              const applyButton = Array.from(applyButtons).find(btn => 
                btn.textContent.toLowerCase().includes('apply') ||
                btn.textContent.toLowerCase().includes('ok') ||
                btn.textContent.toLowerCase().includes('save') ||
                btn.classList.contains('mod-cta')
              );

              if (applyButton && !applyButton.hasAttribute('data-test-listener')) {
                applyButton.setAttribute('data-test-listener', 'true');
                console.log("找到Apply按钮，添加监听器");
                
                applyButton.addEventListener('click', () => {
                  console.log("Apply按钮被点击！");
                  console.log("之前的数据:", previousTaskData);
                  console.log("当前的数据:", currentTaskData);
                  
                  setTimeout(() => {
                    console.log("模拟任务同步处理...");
                    new Notice(`🧪 测试: 检测到任务变化 - ${currentTaskData.description}`);
                  }, 500);
                });
              }

              previousTaskData = { ...currentTaskData };
            }
            break;
          }
        }
      } catch (error) {
        console.error("监听过程中出错:", error);
      }
    };

    // 设置定时器
    const interval = setInterval(windowListener, 300);

    // 设置超时停止
    setTimeout(() => {
      isListening = false;
      clearInterval(interval);
      console.log("测试监听已停止");
      new Notice("🧪 测试监听已停止");
    }, 60000); // 1分钟后停止

    // 提供手动停止方法
    window.stopTestSync = () => {
      isListening = false;
      clearInterval(interval);
      console.log("手动停止测试监听");
      new Notice("🧪 手动停止测试监听");
    };

    return null;

  } catch (error) {
    console.error("测试智能同步时出错:", error);
    new Notice(`❌ 测试失败: ${error.message}`);
    return null;
  }
};

// 辅助函数 - 复制自主脚本
function getProjectInfoFromTechDebtFile(file) {
  if (!file || !file.path) return null;
  
  const techDebtPattern = /^3-过程资产\/([^\/]+)\/技术债\/td-\d{8}-\d{2}\.md$/;
  const match = file.path.match(techDebtPattern);
  
  if (match) {
    return {
      projectName: match[1],
      filePath: file.path,
      fileName: file.name
    };
  }
  
  return null;
}

function generateTargetFilePath(projectName) {
  const now = new Date();
  const year = now.getFullYear();
  const weekNumber = getISOWeekNumber(now);
  const weekString = `WK${weekNumber.toString().padStart(2, '0')}`;
  
  return `2-项目/${projectName}/1-每周计划/Plan-${year}-${weekString}.md`;
}

function getISOWeekNumber(date) {
  const tempDate = new Date(date.valueOf());
  const dayNum = (date.getDay() + 6) % 7;
  tempDate.setDate(tempDate.getDate() - dayNum + 3);
  const firstThursday = tempDate.valueOf();
  tempDate.setMonth(0, 1);
  if (tempDate.getDay() !== 4) {
    tempDate.setMonth(0, 1 + ((4 - tempDate.getDay() + 7) % 7));
  }
  return 1 + Math.ceil((firstThursday - tempDate) / 604800000);
}

function extractTaskDataFromModal(modal) {
  try {
    const allInputs = modal.querySelectorAll('input, textarea');
    let descriptionInput = null;
    let createdInput = null;
    
    allInputs.forEach(input => {
      const placeholder = input.placeholder?.toLowerCase() || '';
      const label = input.getAttribute('aria-label')?.toLowerCase() || '';
      const id = input.id?.toLowerCase() || '';
      
      if (placeholder.includes('description') || label.includes('description') || id.includes('description')) {
        descriptionInput = input;
      }
      
      if (placeholder.includes('created') || label.includes('created') || id.includes('created') ||
          placeholder.includes('date') || label.includes('date')) {
        createdInput = input;
      }
    });
    
    if (!descriptionInput && allInputs.length > 0) {
      descriptionInput = allInputs[0];
    }
    if (!createdInput && allInputs.length > 1) {
      createdInput = allInputs[1];
    }

    return {
      description: descriptionInput ? descriptionInput.value.trim() : "",
      created: createdInput ? createdInput.value.trim() : "",
      hasDescription: descriptionInput ? descriptionInput.value.trim().length > 0 : false,
      hasCreated: createdInput ? createdInput.value.trim().length > 0 : false,
    };
  } catch (error) {
    console.error("提取任务数据时出错:", error);
    return null;
  }
}

function hasTaskDataChanged(previous, current) {
  if (!previous) return true;
  if (!current) return false;
  
  return previous.description !== current.description || 
         previous.created !== current.created ||
         previous.hasDescription !== current.hasDescription ||
         previous.hasCreated !== current.hasCreated;
}
