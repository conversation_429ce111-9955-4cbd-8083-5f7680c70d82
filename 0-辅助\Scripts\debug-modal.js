// 调试模态框检测脚本
// 功能：专门调试任务窗口的检测和数据提取

module.exports = async function (tp) {
  try {
    console.log("=== 开始调试模态框检测 ===");
    
    new Notice("🔍 开始监听模态框，请打开 Tasks 窗口", 5000);
    
    let isListening = true;
    let checkCount = 0;
    
    const modalChecker = () => {
      if (!isListening) return;
      
      checkCount++;
      
      // 查找所有可能的模态框
      const allModals = document.querySelectorAll('.modal, [class*="modal"], [class*="dialog"], .modal-container');
      
      if (allModals.length > 0) {
        console.log(`\n=== 检查 #${checkCount} - 找到 ${allModals.length} 个模态框 ===`);
        
        allModals.forEach((modal, index) => {
          console.log(`\n--- 模态框 ${index + 1} ---`);
          console.log("类名:", modal.className);
          console.log("ID:", modal.id);
          console.log("可见性:", modal.style.display !== 'none' && modal.offsetParent !== null);
          
          // 检查内容
          const textContent = modal.textContent || '';
          console.log("文本内容片段:", textContent.substring(0, 100) + '...');
          
          // 查找输入字段
          const inputs = modal.querySelectorAll('input, textarea, select');
          console.log(`输入字段数量: ${inputs.length}`);
          
          inputs.forEach((input, inputIndex) => {
            console.log(`  输入字段 ${inputIndex + 1}:`);
            console.log(`    类型: ${input.type || input.tagName}`);
            console.log(`    占位符: "${input.placeholder || ''}"`);
            console.log(`    标签: "${input.getAttribute('aria-label') || ''}"`);
            console.log(`    ID: "${input.id || ''}"`);
            console.log(`    名称: "${input.name || ''}"`);
            console.log(`    值: "${input.value || ''}"`);
          });
          
          // 查找按钮
          const buttons = modal.querySelectorAll('button');
          console.log(`按钮数量: ${buttons.length}`);
          
          buttons.forEach((btn, btnIndex) => {
            console.log(`  按钮 ${btnIndex + 1}: "${btn.textContent.trim()}" (${btn.className})`);
          });
          
          // 检查是否是 Tasks 相关
          const isTaskModal = textContent.includes('Task') || 
                             textContent.includes('Description') || 
                             textContent.includes('Created') ||
                             inputs.length > 0;
          
          console.log(`是否为任务模态框: ${isTaskModal}`);
          
          if (isTaskModal && inputs.length > 0) {
            console.log("\n🎯 这可能是 Tasks 模态框！");
            
            // 尝试提取数据
            const taskData = extractTaskData(modal);
            console.log("提取的任务数据:", taskData);
            
            // 监听按钮点击
            buttons.forEach(btn => {
              if (!btn.hasAttribute('data-debug-listener')) {
                btn.setAttribute('data-debug-listener', 'true');
                btn.addEventListener('click', () => {
                  console.log(`🖱️ 按钮被点击: "${btn.textContent.trim()}"`);
                  
                  setTimeout(() => {
                    const updatedData = extractTaskData(modal);
                    console.log("点击后的任务数据:", updatedData);
                  }, 100);
                });
              }
            });
          }
        });
      } else if (checkCount % 10 === 0) {
        console.log(`检查 #${checkCount} - 未找到模态框`);
      }
    };
    
    // 每200ms检查一次
    const interval = setInterval(modalChecker, 200);
    
    // 30秒后停止
    setTimeout(() => {
      isListening = false;
      clearInterval(interval);
      console.log("\n=== 调试结束 ===");
      new Notice("🔍 模态框调试已结束");
    }, 30000);
    
    // 提供手动停止方法
    window.stopModalDebug = () => {
      isListening = false;
      clearInterval(interval);
      console.log("\n=== 手动停止调试 ===");
      new Notice("🔍 手动停止模态框调试");
    };
    
    return null;
    
  } catch (error) {
    console.error("调试模态框时出错:", error);
    new Notice(`❌ 调试失败: ${error.message}`);
    return null;
  }
};

// 提取任务数据的辅助函数
function extractTaskData(modal) {
  try {
    const inputs = modal.querySelectorAll('input, textarea');
    const data = {};
    
    inputs.forEach((input, index) => {
      const key = input.placeholder || input.getAttribute('aria-label') || input.id || input.name || `input_${index}`;
      data[key] = input.value || '';
    });
    
    return data;
  } catch (error) {
    console.error("提取任务数据时出错:", error);
    return null;
  }
}
