# 快速记录突发灵感脚本使用说明

## 功能概述

这个脚本可以快速记录突发灵感，具有以下功能：

1. **快速选择类型**：通过弹窗快速选择"记录洞见/发现"、"记录知识/信息"或"打开改进文档"
2. **便捷输入内容**：弹出对话框输入内容（仅记录类型）
3. **智能处理**：
   - 记录洞见/发现：添加到改进待办事项的缓存区
   - 记录知识/信息：自动打开效率工具箱文件
   - 打开改进文档：直接打开改进待办事项文件
4. **自动创建**：目标文件不存在时自动根据模板创建

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── addInsight.js                      # 主要脚本逻辑
│   └── 快速记录突发灵感脚本使用说明.md    # 本说明文件
└── Templater/
    └── Function/
        └── addInsight.md                  # Templater调用接口
```

## 使用方法

### 设置快捷键（一次性设置）

1. **打开Obsidian设置**：点击左下角齿轮图标
2. **进入快捷键设置**：左侧菜单选择"快捷键"
3. **搜索模板**：在搜索框输入"addInsight"
4. **设置快捷键**：找到"Templater: Open addInsight.md"，点击右侧添加快捷键
5. **输入组合键**：例如 `Alt+3` 或 `Ctrl+Shift+I`

### 日常使用

1. **按下快捷键**：使用你设置的快捷键（如 `Alt+3`）
2. **选择类型**：在弹出的选择器中选择以下选项之一：
   - 【1】记录洞见/发现
   - 【2】记录知识/信息
   - 【3】打开改进文档
3. **输入内容**（仅记录类型）：如果选择了记录类型，在弹出的对话框中输入内容
4. **自动处理**：
   - 记录洞见/发现：自动添加到改进待办事项的缓存区
   - 记录知识/信息：自动打开效率工具箱文件
   - 打开改进文档：直接打开改进待办事项文件

## 目标文件

脚本会根据当前项目动态确定目标文件：
```
2-项目/{项目名称}/改进待办事项.md
```

## 功能说明

### 记录洞见/发现
- 添加到改进待办事项文件的"缓存区"标题下
- 格式：`- 内容`（普通列表项）
- 需要输入具体内容

### 记录知识/信息
- 自动打开效率工具箱文件：`3-过程资产/{项目名称}/其他/效率工具箱.md`
- 如果文件不存在，会自动创建并添加标题
- 需要输入具体内容

### 打开改进文档
- 直接在新标签页打开改进待办事项文件：`2-项目/{项目名称}/改进待办事项.md`
- 如果文件不存在，会自动根据模板创建
- 无需输入内容，直接打开文件

## 故障排除

### 常见问题

1. **"未找到'缓存区'标题"**
   - 检查改进待办事项文件中是否存在包含"缓存区"的标题
   - 如果文件不存在，脚本会自动根据模板创建

2. **"已取消选择"**
   - 用户在选择弹窗中点击了取消或按了ESC键
   - 这是正常操作，无需处理

3. **"请先打开一个项目文件"**
   - 确保当前打开的文件在项目目录下（路径格式：`2-项目/{项目名}/...`）
   - 脚本需要从当前文件路径提取项目名称

4. **快捷键不起作用**
   - 确认快捷键设置正确
   - 检查是否有其他插件占用了相同的快捷键

### 调试信息

脚本会在控制台输出详细的错误信息，可通过以下步骤查看：
1. 按 `Ctrl+Shift+I` 打开开发者工具
2. 切换到 Console 标签页
3. 查看红色错误信息

## 自定义修改

如需修改脚本行为，可编辑 `0-辅助/Scripts/addInsight.js` 文件：

- **修改选项文本**：更改 `suggester` 函数的第一个参数数组
- **修改选项值**：更改 `suggester` 函数的第二个参数数组
- **修改文件路径**：更改 `fileConfigs` 对象中的路径配置
- **修改列表格式**：更改 `newTask` 变量的定义