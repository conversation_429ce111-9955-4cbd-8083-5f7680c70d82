// 文件名：sync-task.js
// 功能：打开任务创建窗口，捕获创建的任务并复制到目标文件

module.exports = async function (params) {
  const { app, moment, file, workspace } = this.app;
  const targetFilePath = "Daily Notes/Tasks.md"; //  ⚠️ 修改为目标文件路径
  const targetSection = "## Archived Tasks"; //  ⚠️ 修改为目标标题

  // 获取Tasks插件实例
  const tasksPlugin = app.plugins.getPlugin("obsidian-tasks-plugin");
  if (!tasksPlugin) {
    new Notice("❌ Tasks插件未启用");
    return;
  }

  // 获取当前文件内容（用于恢复上下文）
  const originalContent = await app.vault.read(file);

  // 打开任务创建对话框
  await tasksPlugin.createOrEditTaskCallback();

  // 监听编辑器变化
  let timeout;
  const handleChange = async () => {
    clearTimeout(timeout);
    timeout = setTimeout(async () => {
      // 获取新创建的任务内容
      const newContent = await app.vault.read(file);
      const diff = getContentDiff(originalContent, newContent);

      if (diff) {
        // 关闭监听
        app.workspace.off("editor-change", handleChange);

        // 添加到目标文件
        await addToTargetFile(diff, targetFilePath, targetSection);

        // 恢复原始内容（移除临时任务）
        await app.vault.modify(file, originalContent);

        new Notice(`✓ 任务已同步到 ${targetFilePath}`);
      }
    }, 500); // 等待编辑完成
  };

  app.workspace.on("editor-change", handleChange);
};

// 获取内容差异（新创建的任务）
function getContentDiff(original, current) {
  const originalLines = original.split("\n");
  const currentLines = current.split("\n");

  for (let i = 0; i < currentLines.length; i++) {
    if (i >= originalLines.length || originalLines[i] !== currentLines[i]) {
      return currentLines[i]; // 返回新增的任务行
    }
  }
  return null;
}

// 添加到目标文件
async function addToTargetFile(content, filePath, sectionHeader) {
  const targetFile = app.vault.getAbstractFileByPath(filePath);
  if (!targetFile) {
    new Notice(`❌ 目标文件不存在: ${filePath}`);
    return;
  }

  let fileContent = await app.vault.read(targetFile);
  const sectionIndex = fileContent.indexOf(sectionHeader);

  if (sectionIndex === -1) {
    // 如果目标位置不存在，添加到文件末尾
    fileContent += `\n${sectionHeader}\n${content}\n`;
  } else {
    // 插入到指定标题下方
    const insertionPoint = sectionIndex + sectionHeader.length;
    fileContent =
      fileContent.substring(0, insertionPoint) +
      `\n${content}\n` +
      fileContent.substring(insertionPoint);
  }

  await app.vault.modify(targetFile, fileContent);
}
