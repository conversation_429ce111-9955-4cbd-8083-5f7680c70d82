// 文件名：sync-task.js
// 功能：打开任务创建窗口，捕获创建的任务并复制到目标文件

module.exports = async function (params) {
  const { app, moment, file, workspace } = this.app;
  const targetFilePath = "2-项目/PKMS/1-每周计划/Plan-2025-WK33.md"; //  ⚠️ 修改为目标文件路径
  const targetSection = "# 5. 任务拆解"; //  ⚠️ 修改为目标标题

  let handleChange = null; // 用于清理事件监听器
  let timeout = null;

  try {
    // 获取Tasks插件实例
    const tasksPlugin = app.plugins.getPlugin("obsidian-tasks-plugin");
    if (!tasksPlugin) {
      new Notice("❌ Tasks插件未启用");
      return;
    }

    // 检查目标文件是否存在
    const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    if (!targetFile) {
      new Notice(`❌ 目标文件不存在: ${targetFilePath}`);
      return;
    }

    // 获取当前文件内容（用于恢复上下文）
    const originalContent = await app.vault.read(file);

    // 设置超时清理机制（30秒后自动清理）
    const cleanupTimeout = setTimeout(() => {
      if (handleChange) {
        app.workspace.off("editor-change", handleChange);
        new Notice("⚠️ 任务创建超时，已取消监听");
      }
    }, 30000);

    // 监听编辑器变化
    handleChange = async () => {
      try {
        clearTimeout(timeout);
        timeout = setTimeout(async () => {
          try {
            // 获取新创建的任务内容
            const newContent = await app.vault.read(file);
            const diff = getContentDiff(originalContent, newContent);

            if (diff && diff.length > 0) {
              // 关闭监听和清理
              app.workspace.off("editor-change", handleChange);
              clearTimeout(cleanupTimeout);

              // 添加到目标文件
              const success = await addToTargetFile(
                diff,
                targetFilePath,
                targetSection,
                app
              );

              if (success) {
                // 恢复原始内容（移除临时任务）
                await app.vault.modify(file, originalContent);
                new Notice(`✓ 任务已同步到 ${targetFilePath}`);
              }
            }
          } catch (error) {
            console.error("处理任务变化时出错:", error);
            new Notice(`❌ 处理任务时出错: ${error.message}`);
            // 清理监听器
            if (handleChange) {
              app.workspace.off("editor-change", handleChange);
            }
          }
        }, 500); // 等待编辑完成
      } catch (error) {
        console.error("编辑器变化处理出错:", error);
      }
    };

    app.workspace.on("editor-change", handleChange);

    // 打开任务创建对话框
    await tasksPlugin.createOrEditTaskCallback();
  } catch (error) {
    console.error("sync-task脚本执行出错:", error);
    new Notice(`❌ 脚本执行失败: ${error.message}`);

    // 清理资源
    if (handleChange) {
      app.workspace.off("editor-change", handleChange);
    }
    if (timeout) {
      clearTimeout(timeout);
    }
  }
};

// 获取内容差异（新创建的任务）
function getContentDiff(original, current) {
  try {
    const originalLines = original.split("\n");
    const currentLines = current.split("\n");
    const newTasks = [];

    // 检查新增的行
    for (let i = 0; i < currentLines.length; i++) {
      if (i >= originalLines.length || originalLines[i] !== currentLines[i]) {
        const line = currentLines[i].trim();
        // 检查是否为任务行（以 - [ ] 或 - [x] 开头）
        if (line.match(/^-\s*\[[\sx]\]\s+.+/)) {
          newTasks.push(line);
        }
      }
    }

    // 如果没有找到新任务，检查是否有行被修改（可能是任务编辑）
    if (newTasks.length === 0) {
      for (
        let i = 0;
        i < Math.min(originalLines.length, currentLines.length);
        i++
      ) {
        if (originalLines[i] !== currentLines[i]) {
          const line = currentLines[i].trim();
          if (line.match(/^-\s*\[[\sx]\]\s+.+/)) {
            newTasks.push(line);
            break; // 只取第一个修改的任务
          }
        }
      }
    }

    return newTasks;
  } catch (error) {
    console.error("获取内容差异时出错:", error);
    return [];
  }
}

// 添加到目标文件
async function addToTargetFile(
  tasks,
  filePath,
  sectionHeader,
  appInstance = app
) {
  try {
    const targetFile = appInstance.vault.getAbstractFileByPath(filePath);
    if (!targetFile) {
      new Notice(`❌ 目标文件不存在: ${filePath}`);
      return false;
    }

    if (!tasks || tasks.length === 0) {
      new Notice("⚠️ 没有找到有效的任务内容");
      return false;
    }

    let fileContent = await appInstance.vault.read(targetFile);
    const tasksText = Array.isArray(tasks) ? tasks.join("\n") : tasks;

    // 检查任务是否已存在（避免重复添加）
    if (fileContent.includes(tasksText)) {
      new Notice("⚠️ 任务已存在，跳过添加");
      return false;
    }

    const sectionIndex = fileContent.indexOf(sectionHeader);

    if (sectionIndex === -1) {
      // 如果目标位置不存在，添加到文件末尾
      fileContent += `\n\n${sectionHeader}\n${tasksText}\n`;
    } else {
      // 找到标题后的第一个换行符位置
      const headerEndIndex = sectionIndex + sectionHeader.length;
      const nextLineIndex = fileContent.indexOf("\n", headerEndIndex);

      if (nextLineIndex === -1) {
        // 如果标题在文件末尾
        fileContent += `\n${tasksText}\n`;
      } else {
        // 在标题下方插入任务
        fileContent =
          fileContent.substring(0, nextLineIndex + 1) +
          `${tasksText}\n` +
          fileContent.substring(nextLineIndex + 1);
      }
    }

    await appInstance.vault.modify(targetFile, fileContent);
    return true;
  } catch (error) {
    console.error("添加到目标文件时出错:", error);
    new Notice(`❌ 添加任务失败: ${error.message}`);
    return false;
  }
}
