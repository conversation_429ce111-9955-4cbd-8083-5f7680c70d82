// 文件名：sync-task.js
// 功能：智能任务同步 - 从技术债文件同步任务到项目计划文件

module.exports = async function (params) {
  const { app, moment, file, workspace } = this.app;

  // 检查当前文件是否为技术债文件
  const projectInfo = getProjectInfoFromTechDebtFile(file);
  if (!projectInfo) {
    new Notice("❌ 当前文件不是技术债文件，无法启用任务同步");
    return;
  }

  // 动态生成目标文件路径
  const targetFilePath = generateTargetFilePath(projectInfo.projectName);
  const targetSection = "# 5. 任务拆解";

  console.log("项目信息:", projectInfo);
  console.log("目标文件:", targetFilePath);

  let handleChange = null; // 用于清理事件监听器
  let timeout = null;
  let currentTaskData = null; // 存储当前任务数据

  try {
    // 获取Tasks插件实例
    const tasksPlugin = app.plugins.getPlugin("obsidian-tasks-plugin");
    if (!tasksPlugin) {
      new Notice("❌ Tasks插件未启用");
      return;
    }

    // 检查目标文件是否存在
    const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    if (!targetFile) {
      new Notice(`❌ 目标文件不存在: ${targetFilePath}`);
      return;
    }

    // 显示启动消息
    new Notice(`✅ 任务同步已启动 - 项目: ${projectInfo.projectName}`, 3000);

    // 创建任务窗口监听器
    const taskWindowListener = createTaskWindowListener(
      targetFilePath,
      targetSection
    );

    // 设置定时器监听任务窗口
    const windowCheckInterval = setInterval(taskWindowListener, 200);

    // 设置清理定时器
    const cleanupTimer = setTimeout(() => {
      clearInterval(windowCheckInterval);
      new Notice("⏰ 任务同步监听已超时停止");
      console.log("任务同步监听超时");
    }, 300000); // 5分钟超时

    // 存储清理函数到全局，以便手动停止
    window.stopTaskSync = () => {
      clearInterval(windowCheckInterval);
      clearTimeout(cleanupTimer);
      new Notice("🛑 任务同步已手动停止");
      console.log("任务同步已手动停止");
    };

    // 执行 Tasks 插件的创建任务命令
    try {
      // 使用正确的 Tasks 命令 ID（从调试中找到的）
      const taskCommandId = "obsidian-tasks-plugin:edit-task";

      // 尝试执行命令
      await app.commands.executeCommandById(taskCommandId);
      console.log("Tasks 创建命令已执行");
    } catch (error) {
      console.error("执行 Tasks 创建命令时出错:", error);

      // 如果直接命令失败，尝试查找命令
      try {
        const commands = app.commands.listCommands();
        const createTaskCommand = commands.find(
          (cmd) =>
            cmd.name === "Tasks: Create or edit task" ||
            cmd.id.includes("create-or-edit-task") ||
            (cmd.id.includes("task") &&
              cmd.name.toLowerCase().includes("create"))
        );

        if (createTaskCommand) {
          console.log("找到 Tasks 命令:", createTaskCommand.id);
          await app.commands.executeCommandById(createTaskCommand.id);
        } else {
          // 如果还是找不到，提示用户手动创建
          new Notice(
            "请在当前文件中创建一个任务，脚本将自动同步到目标文件",
            5000
          );
        }
      } catch (fallbackError) {
        console.error("备用方法也失败:", fallbackError);
        new Notice(
          "请在当前文件中创建一个任务，脚本将自动同步到目标文件",
          5000
        );
      }
    }
  } catch (error) {
    console.error("sync-task脚本执行出错:", error);
    new Notice(`❌ 脚本执行失败: ${error.message}`);

    // 清理资源
    if (handleChange) {
      app.workspace.off("editor-change", handleChange);
    }
    if (timeout) {
      clearTimeout(timeout);
    }
  }
};

// 检查文件是否为技术债文件并提取项目信息
function getProjectInfoFromTechDebtFile(file) {
  if (!file || !file.path) return null;

  // 检查路径是否匹配技术债文件模式
  // 格式：3-过程资产/项目名称/技术债/td-YYYYMMDD-XX.md
  const techDebtPattern = /^3-过程资产\/([^\/]+)\/技术债\/td-\d{8}-\d{2}\.md$/;
  const match = file.path.match(techDebtPattern);

  if (match) {
    return {
      projectName: match[1],
      filePath: file.path,
      fileName: file.name,
    };
  }

  return null;
}

// 生成目标文件路径（动态计算周数）
function generateTargetFilePath(projectName) {
  const now = new Date();
  const year = now.getFullYear();

  // 计算ISO周数
  const weekNumber = getISOWeekNumber(now);
  const weekString = `WK${weekNumber.toString().padStart(2, "0")}`;

  return `2-项目/${projectName}/1-每周计划/Plan-${year}-${weekString}.md`;
}

// 获取ISO周数
function getISOWeekNumber(date) {
  const tempDate = new Date(date.valueOf());
  const dayNum = (date.getDay() + 6) % 7;
  tempDate.setDate(tempDate.getDate() - dayNum + 3);
  const firstThursday = tempDate.valueOf();
  tempDate.setMonth(0, 1);
  if (tempDate.getDay() !== 4) {
    tempDate.setMonth(0, 1 + ((4 - tempDate.getDay() + 7) % 7));
  }
  return 1 + Math.ceil((firstThursday - tempDate) / 604800000);
}

// 监听任务窗口的变化
function createTaskWindowListener(targetFilePath, targetSection) {
  let previousTaskData = null;

  return async function () {
    try {
      // 检测任务窗口
      const taskModal = document.querySelector(".modal.tasks-modal");
      if (!taskModal) return;

      // 提取任务数据
      const currentTaskData = extractTaskDataFromModal(taskModal);
      if (!currentTaskData) return;

      // 检查是否有变化
      if (hasTaskDataChanged(previousTaskData, currentTaskData)) {
        console.log("检测到任务数据变化:", currentTaskData);

        // 监听Apply按钮点击
        const applyButton = taskModal.querySelector(
          'button[aria-label="Apply"], .mod-cta'
        );
        if (applyButton && !applyButton.hasAttribute("data-sync-listener")) {
          applyButton.setAttribute("data-sync-listener", "true");

          applyButton.addEventListener("click", async () => {
            console.log("Apply按钮被点击，准备同步任务");

            // 延迟执行，确保任务窗口关闭后再同步
            setTimeout(async () => {
              await handleTaskSync(
                previousTaskData,
                currentTaskData,
                targetFilePath,
                targetSection
              );
            }, 500);
          });
        }

        previousTaskData = { ...currentTaskData };
      }
    } catch (error) {
      console.error("监听任务窗口时出错:", error);
    }
  };
}

// 从任务模态框提取数据
function extractTaskDataFromModal(modal) {
  try {
    // 更广泛的选择器来查找输入字段
    const allInputs = modal.querySelectorAll("input, textarea");
    let descriptionInput = null;
    let createdInput = null;

    // 遍历所有输入字段，根据标签或占位符文本识别
    allInputs.forEach((input) => {
      const placeholder = input.placeholder?.toLowerCase() || "";
      const label = input.getAttribute("aria-label")?.toLowerCase() || "";
      const id = input.id?.toLowerCase() || "";
      const name = input.name?.toLowerCase() || "";

      // 查找描述字段
      if (
        placeholder.includes("description") ||
        label.includes("description") ||
        id.includes("description") ||
        name.includes("description")
      ) {
        descriptionInput = input;
      }

      // 查找创建日期字段
      if (
        placeholder.includes("created") ||
        label.includes("created") ||
        id.includes("created") ||
        name.includes("created") ||
        placeholder.includes("date") ||
        label.includes("date")
      ) {
        createdInput = input;
      }
    });

    // 如果没有找到特定字段，尝试按顺序获取
    if (!descriptionInput && allInputs.length > 0) {
      descriptionInput = allInputs[0]; // 通常第一个是描述
    }
    if (!createdInput && allInputs.length > 1) {
      createdInput = allInputs[1]; // 通常第二个是日期
    }

    const result = {
      description: descriptionInput ? descriptionInput.value.trim() : "",
      created: createdInput ? createdInput.value.trim() : "",
      hasDescription: descriptionInput
        ? descriptionInput.value.trim().length > 0
        : false,
      hasCreated: createdInput ? createdInput.value.trim().length > 0 : false,
    };

    console.log("提取的任务数据:", result);
    return result;
  } catch (error) {
    console.error("提取任务数据时出错:", error);
    return null;
  }
}

// 检查任务数据是否发生变化
function hasTaskDataChanged(previous, current) {
  if (!previous) return true;
  if (!current) return false;

  return (
    previous.description !== current.description ||
    previous.created !== current.created ||
    previous.hasDescription !== current.hasDescription ||
    previous.hasCreated !== current.hasCreated
  );
}

// 处理任务同步逻辑
async function handleTaskSync(
  previousData,
  currentData,
  targetFilePath,
  targetSection
) {
  try {
    console.log("开始处理任务同步");
    console.log("之前的数据:", previousData);
    console.log("当前的数据:", currentData);

    const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    if (!targetFile) {
      new Notice(`❌ 目标文件不存在: ${targetFilePath}`);
      return;
    }

    // 情况1: Description从无到有 - 新建任务
    if (!previousData?.hasDescription && currentData.hasDescription) {
      console.log("情况1: 新建任务");
      await addNewTask(currentData, targetFile, targetSection);
      new Notice(`✅ 新任务已同步: ${currentData.description}`);
    }
    // 情况2: Created发生变化 - 更新现有任务
    else if (
      previousData?.created !== currentData.created &&
      currentData.hasDescription
    ) {
      console.log("情况2: 更新任务Created");
      await updateTaskCreated(
        previousData,
        currentData,
        targetFile,
        targetSection
      );
      new Notice(`✅ 任务Created已更新: ${currentData.description}`);
    }
    // 情况3: Description发生变化 - 删除旧任务，添加新任务
    else if (
      previousData?.description !== currentData.description &&
      previousData?.hasDescription &&
      currentData.hasDescription
    ) {
      console.log("情况3: 更新任务Description");
      await updateTaskDescription(
        previousData,
        currentData,
        targetFile,
        targetSection
      );
      new Notice(
        `✅ 任务已更新: ${previousData.description} → ${currentData.description}`
      );
    }
  } catch (error) {
    console.error("处理任务同步时出错:", error);
    new Notice(`❌ 任务同步失败: ${error.message}`);
  }
}

// 添加新任务
async function addNewTask(taskData, targetFile, targetSection) {
  try {
    let fileContent = await app.vault.read(targetFile);

    // 生成任务行
    const taskLine = generateTaskLine(taskData);

    // 查找目标章节并插入任务
    const updatedContent = insertTaskIntoSection(
      fileContent,
      targetSection,
      taskLine
    );

    await app.vault.modify(targetFile, updatedContent);
    console.log("新任务已添加:", taskLine);
  } catch (error) {
    console.error("添加新任务时出错:", error);
    throw error;
  }
}

// 更新任务的Created信息
async function updateTaskCreated(
  previousData,
  currentData,
  targetFile,
  targetSection
) {
  try {
    let fileContent = await app.vault.read(targetFile);

    // 查找现有任务
    const existingTaskPattern = new RegExp(
      `^(- \\[[ x]\\] .*${escapeRegExp(previousData.description)}.*)$`,
      "gm"
    );

    const match = fileContent.match(existingTaskPattern);
    if (match) {
      // 生成新的任务行
      const newTaskLine = generateTaskLine(currentData);

      // 替换现有任务
      fileContent = fileContent.replace(existingTaskPattern, newTaskLine);

      await app.vault.modify(targetFile, fileContent);
      console.log("任务Created已更新:", newTaskLine);
    } else {
      console.log("未找到要更新的任务，添加新任务");
      await addNewTask(currentData, targetFile, targetSection);
    }
  } catch (error) {
    console.error("更新任务Created时出错:", error);
    throw error;
  }
}

// 更新任务的Description
async function updateTaskDescription(
  previousData,
  currentData,
  targetFile,
  targetSection
) {
  try {
    let fileContent = await app.vault.read(targetFile);

    // 查找并删除旧任务
    const oldTaskPattern = new RegExp(
      `^- \\[[ x]\\] .*${escapeRegExp(previousData.description)}.*\\n?`,
      "gm"
    );

    fileContent = fileContent.replace(oldTaskPattern, "");

    // 添加新任务
    const newTaskLine = generateTaskLine(currentData);
    const updatedContent = insertTaskIntoSection(
      fileContent,
      targetSection,
      newTaskLine
    );

    await app.vault.modify(targetFile, updatedContent);
    console.log("任务Description已更新:", newTaskLine);
  } catch (error) {
    console.error("更新任务Description时出错:", error);
    throw error;
  }
}

// 生成任务行
function generateTaskLine(taskData) {
  let taskLine = `- [ ] ${taskData.description}`;

  if (taskData.hasCreated && taskData.created) {
    taskLine += ` ➕ ${taskData.created}`;
  }

  return taskLine;
}

// 在指定章节中插入任务
function insertTaskIntoSection(fileContent, sectionHeader, taskLine) {
  const sectionIndex = fileContent.indexOf(sectionHeader);

  if (sectionIndex === -1) {
    // 如果章节不存在，添加到文件末尾
    return fileContent + `\n\n${sectionHeader}\n${taskLine}\n`;
  } else {
    // 在章节下方插入任务
    const headerEndIndex = sectionIndex + sectionHeader.length;
    const nextLineIndex = fileContent.indexOf("\n", headerEndIndex);

    if (nextLineIndex === -1) {
      return fileContent + `\n${taskLine}\n`;
    } else {
      return (
        fileContent.substring(0, nextLineIndex + 1) +
        `${taskLine}\n` +
        fileContent.substring(nextLineIndex + 1)
      );
    }
  }
}

// 转义正则表达式特殊字符
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
