// 测试任务同步脚本
// 功能：简化版本的任务同步，用于测试和调试

module.exports = async function (tp) {
  try {
    console.log("开始执行任务同步测试...");

    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("❌ 请先打开一个文件");
      return null;
    }

    console.log("当前文件:", activeFile.path);

    // 配置目标文件
    const targetFilePath = "2-项目/PKMS/1-每周计划/Plan-2025-WK33.md";
    const targetSection = "# 5. 任务拆解";

    // 检查 Tasks 插件
    const tasksPlugin = app.plugins.getPlugin("obsidian-tasks-plugin");
    if (!tasksPlugin) {
      new Notice("❌ Tasks插件未启用");
      return null;
    }

    console.log("Tasks插件已找到");

    // 检查目标文件
    const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    if (!targetFile) {
      new Notice(`❌ 目标文件不存在: ${targetFilePath}`);
      return null;
    }

    console.log("目标文件已找到:", targetFilePath);

    // 获取原始内容
    const originalContent = await app.vault.read(activeFile);
    console.log("已保存原始内容，长度:", originalContent.length);

    // 显示准备就绪的消息
    new Notice("✓ 准备就绪，即将打开任务创建窗口...");

    // 设置监听器
    let isListening = true;
    let timeout = null;

    const handleChange = async () => {
      if (!isListening) return;

      console.log("检测到编辑器变化");
      clearTimeout(timeout);

      timeout = setTimeout(async () => {
        try {
          const newContent = await app.vault.read(activeFile);
          console.log("新内容长度:", newContent.length);

          // 简单的差异检测
          if (newContent !== originalContent) {
            console.log("检测到内容变化");

            // 停止监听
            isListening = false;
            app.workspace.off("editor-change", handleChange);

            // 查找新增的任务行
            const originalLines = originalContent.split("\n");
            const newLines = newContent.split("\n");
            const newTasks = [];

            for (let i = 0; i < newLines.length; i++) {
              if (
                i >= originalLines.length ||
                originalLines[i] !== newLines[i]
              ) {
                const line = newLines[i].trim();
                if (line.match(/^-\s*\[[\sx]\]\s+.+/)) {
                  newTasks.push(line);
                  console.log("找到新任务:", line);
                }
              }
            }

            if (newTasks.length > 0) {
              // 添加到目标文件
              const success = await addTaskToTarget(
                newTasks,
                targetFilePath,
                targetSection
              );

              if (success) {
                // 恢复原始内容
                await app.vault.modify(activeFile, originalContent);
                new Notice(`✓ 成功同步 ${newTasks.length} 个任务到目标文件`);
                console.log("任务同步完成");
              }
            } else {
              new Notice("⚠️ 未检测到有效的任务格式");
              console.log("未找到有效任务");
            }
          }
        } catch (error) {
          console.error("处理变化时出错:", error);
          new Notice(`❌ 处理失败: ${error.message}`);
        }
      }, 1000); // 增加延迟到1秒
    };

    // 设置超时清理
    setTimeout(() => {
      if (isListening) {
        isListening = false;
        app.workspace.off("editor-change", handleChange);
        new Notice("⚠️ 监听超时，已自动停止");
        console.log("监听超时");
      }
    }, 30000);

    // 开始监听
    app.workspace.on("editor-change", handleChange);
    console.log("开始监听编辑器变化");

    // 尝试不同的方法打开任务创建窗口
    console.log("Tasks插件对象:", tasksPlugin);
    console.log("Tasks插件方法:", Object.getOwnPropertyNames(tasksPlugin));

    // 方法1: 尝试执行 Tasks 插件的创建任务命令
    try {
      // 查找 Tasks 插件的命令
      const commands = app.commands.listCommands();
      const taskCommands = commands.filter(
        (cmd) => cmd.id.includes("task") || cmd.id.includes("Tasks")
      );
      console.log(
        "找到的任务相关命令:",
        taskCommands.map((cmd) => ({ id: cmd.id, name: cmd.name }))
      );

      // 尝试执行创建任务的命令
      const createTaskCommand = taskCommands.find(
        (cmd) =>
          cmd.id.includes("create") || cmd.name.toLowerCase().includes("create")
      );

      if (createTaskCommand) {
        console.log("执行命令:", createTaskCommand.id);
        await app.commands.executeCommandById(createTaskCommand.id);
        console.log("任务创建命令已执行");
      } else {
        // 方法2: 如果没有找到创建命令，尝试其他方法
        console.log("未找到创建任务命令，尝试其他方法");

        // 检查插件是否有公开的方法
        if (typeof tasksPlugin.createTask === "function") {
          await tasksPlugin.createTask();
        } else if (typeof tasksPlugin.openCreateTaskModal === "function") {
          await tasksPlugin.openCreateTaskModal();
        } else {
          // 方法3: 手动提示用户创建任务
          new Notice("请手动创建一个任务，脚本将自动检测并同步");
          console.log("等待用户手动创建任务");
        }
      }
    } catch (error) {
      console.error("执行任务创建命令时出错:", error);
      new Notice("请手动创建一个任务，脚本将自动检测并同步");
    }

    return null;
  } catch (error) {
    console.error("测试脚本执行出错:", error);
    new Notice(`❌ 测试失败: ${error.message}`);
    return null;
  }
};

// 添加任务到目标文件的辅助函数
async function addTaskToTarget(tasks, filePath, sectionHeader) {
  try {
    const targetFile = app.vault.getAbstractFileByPath(filePath);
    let fileContent = await app.vault.read(targetFile);

    const tasksText = tasks.join("\n");
    console.log("准备添加的任务文本:", tasksText);

    // 查找目标章节
    const sectionIndex = fileContent.indexOf(sectionHeader);
    if (sectionIndex === -1) {
      console.log("未找到目标章节，添加到文件末尾");
      fileContent += `\n\n${sectionHeader}\n${tasksText}\n`;
    } else {
      console.log("找到目标章节，插入任务");
      const headerEndIndex = sectionIndex + sectionHeader.length;
      const nextLineIndex = fileContent.indexOf("\n", headerEndIndex);

      if (nextLineIndex === -1) {
        fileContent += `\n${tasksText}\n`;
      } else {
        fileContent =
          fileContent.substring(0, nextLineIndex + 1) +
          `${tasksText}\n` +
          fileContent.substring(nextLineIndex + 1);
      }
    }

    await app.vault.modify(targetFile, fileContent);
    console.log("任务已成功添加到目标文件");
    return true;
  } catch (error) {
    console.error("添加任务到目标文件时出错:", error);
    return false;
  }
}
