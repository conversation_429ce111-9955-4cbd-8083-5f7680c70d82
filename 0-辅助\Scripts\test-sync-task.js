// 测试任务同步脚本
// 功能：简化版本的任务同步，用于测试和调试

module.exports = async function (tp) {
  try {
    console.log("开始执行任务同步测试...");

    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("❌ 请先打开一个文件");
      return null;
    }

    console.log("当前文件:", activeFile.path);

    // 配置目标文件
    const targetFilePath = "2-项目/PKMS/1-每周计划/Plan-2025-WK33.md";
    const targetSection = "# 5. 任务拆解";

    // 检查 Tasks 插件
    const tasksPlugin = app.plugins.getPlugin("obsidian-tasks-plugin");
    if (!tasksPlugin) {
      new Notice("❌ Tasks插件未启用");
      return null;
    }

    console.log("Tasks插件已找到");

    // 检查目标文件
    const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    if (!targetFile) {
      new Notice(`❌ 目标文件不存在: ${targetFilePath}`);
      return null;
    }

    console.log("目标文件已找到:", targetFilePath);

    // 获取原始内容
    const originalContent = await app.vault.read(activeFile);
    console.log("已保存原始内容，长度:", originalContent.length);

    // 显示准备就绪的消息
    new Notice("✓ 准备就绪，即将打开任务创建窗口...");

    // 设置监听器
    let isListening = true;
    let timeout = null;

    const handleChange = async () => {
      if (!isListening) return;

      console.log("检测到编辑器变化");
      clearTimeout(timeout);

      timeout = setTimeout(async () => {
        try {
          const newContent = await app.vault.read(activeFile);
          console.log("新内容长度:", newContent.length);

          // 简单的差异检测
          if (newContent !== originalContent) {
            console.log("检测到内容变化");

            // 停止监听
            isListening = false;
            app.workspace.off("editor-change", handleChange);

            // 查找新增的任务行
            const originalLines = originalContent.split("\n");
            const newLines = newContent.split("\n");
            const newTasks = [];

            for (let i = 0; i < newLines.length; i++) {
              if (
                i >= originalLines.length ||
                originalLines[i] !== newLines[i]
              ) {
                const line = newLines[i].trim();
                if (line.match(/^-\s*\[[\sx]\]\s+.+/)) {
                  newTasks.push(line);
                  console.log("找到新任务:", line);
                }
              }
            }

            if (newTasks.length > 0) {
              // 添加到目标文件
              const success = await addTaskToTarget(
                newTasks,
                targetFilePath,
                targetSection
              );

              if (success) {
                // 恢复原始内容
                await app.vault.modify(activeFile, originalContent);
                new Notice(`✓ 成功同步 ${newTasks.length} 个任务到目标文件`);
                console.log("任务同步完成");
              }
            } else {
              new Notice("⚠️ 未检测到有效的任务格式");
              console.log("未找到有效任务");
            }
          }
        } catch (error) {
          console.error("处理变化时出错:", error);
          new Notice(`❌ 处理失败: ${error.message}`);
        }
      }, 1000); // 增加延迟到1秒
    };

    // 设置超时清理
    setTimeout(() => {
      if (isListening) {
        isListening = false;
        app.workspace.off("editor-change", handleChange);
        new Notice("⚠️ 监听超时，已自动停止");
        console.log("监听超时");
      }
    }, 30000);

    // 开始监听
    app.workspace.on("editor-change", handleChange);
    console.log("开始监听编辑器变化");

    // 尝试不同的方法打开任务创建窗口
    console.log("Tasks插件对象:", tasksPlugin);
    console.log("Tasks插件方法:", Object.getOwnPropertyNames(tasksPlugin));

    // 首先列出所有可用的命令，找到正确的 Tasks 命令
    const allCommands = app.commands.listCommands();
    const taskCommands = allCommands.filter(cmd =>
      cmd.id.includes("task") ||
      cmd.name.toLowerCase().includes("task") ||
      cmd.id.includes("obsidian-tasks-plugin")
    );

    console.log("所有 Tasks 相关命令:");
    taskCommands.forEach(cmd => {
      console.log(`  ID: ${cmd.id}, Name: ${cmd.name}`);
    });

    // 执行 Tasks 插件的创建任务命令
    try {
      // 尝试几个可能的命令 ID
      const possibleCommandIds = [
        "obsidian-tasks-plugin:create-or-edit-task",
        "obsidian-tasks-plugin:create-task",
        "obsidian-tasks-plugin:edit-task",
        "tasks:create-or-edit-task",
        "tasks:create-task"
      ];

      let commandExecuted = false;

      for (const commandId of possibleCommandIds) {
        try {
          console.log("尝试执行命令:", commandId);
          await app.commands.executeCommandById(commandId);
          console.log("✅ 命令执行成功:", commandId);
          commandExecuted = true;
          break;
        } catch (error) {
          console.log("❌ 命令执行失败:", commandId, error.message);
        }
      }

      if (!commandExecuted) {
        console.log("所有预设命令都失败，尝试从命令列表中查找");
        throw new Error("预设命令都失败");
      }
    } catch (error) {
      console.error("执行 Tasks 命令失败:", error);

      // 备用方法：查找命令
      try {
        const commands = app.commands.listCommands();
        const taskCommands = commands.filter(
          (cmd) =>
            cmd.id.includes("task") || cmd.name.toLowerCase().includes("task")
        );

        console.log(
          "找到的任务相关命令:",
          taskCommands.map((cmd) => ({ id: cmd.id, name: cmd.name }))
        );

        const createTaskCommand = taskCommands.find(
          (cmd) =>
            cmd.name === "Tasks: Create or edit task" ||
            cmd.id.includes("create-or-edit-task") ||
            cmd.name.toLowerCase().includes("create")
        );

        if (createTaskCommand) {
          console.log("找到备用命令:", createTaskCommand.id);
          await app.commands.executeCommandById(createTaskCommand.id);
          console.log("✅ 备用命令执行成功");
        } else {
          console.log("未找到 Tasks 创建命令，提示用户手动创建");
          new Notice("请手动创建一个任务，脚本将自动检测并同步", 5000);
        }
      } catch (fallbackError) {
        console.error("备用方法也失败:", fallbackError);
        new Notice("请手动创建一个任务，脚本将自动检测并同步", 5000);
      }
    }

    return null;
  } catch (error) {
    console.error("测试脚本执行出错:", error);
    new Notice(`❌ 测试失败: ${error.message}`);
    return null;
  }
};

// 添加任务到目标文件的辅助函数
async function addTaskToTarget(tasks, filePath, sectionHeader) {
  try {
    const targetFile = app.vault.getAbstractFileByPath(filePath);
    let fileContent = await app.vault.read(targetFile);

    const tasksText = tasks.join("\n");
    console.log("准备添加的任务文本:", tasksText);

    // 查找目标章节
    const sectionIndex = fileContent.indexOf(sectionHeader);
    if (sectionIndex === -1) {
      console.log("未找到目标章节，添加到文件末尾");
      fileContent += `\n\n${sectionHeader}\n${tasksText}\n`;
    } else {
      console.log("找到目标章节，插入任务");
      const headerEndIndex = sectionIndex + sectionHeader.length;
      const nextLineIndex = fileContent.indexOf("\n", headerEndIndex);

      if (nextLineIndex === -1) {
        fileContent += `\n${tasksText}\n`;
      } else {
        fileContent =
          fileContent.substring(0, nextLineIndex + 1) +
          `${tasksText}\n` +
          fileContent.substring(nextLineIndex + 1);
      }
    }

    await app.vault.modify(targetFile, fileContent);
    console.log("任务已成功添加到目标文件");
    return true;
  } catch (error) {
    console.error("添加任务到目标文件时出错:", error);
    return false;
  }
}
