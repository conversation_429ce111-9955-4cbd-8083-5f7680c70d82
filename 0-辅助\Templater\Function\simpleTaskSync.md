<%*
// 简化任务同步调用模板
// 功能：启动简化的任务同步功能

try {
  console.log("启动简化任务同步...");
  
  // 调用简化任务同步脚本
  const syncScript = tp.user["simple-task-sync"];
  if (typeof syncScript === 'function') {
    await syncScript(tp);
  } else {
    console.error("简化任务同步脚本未找到或不是函数");
    new Notice("❌ 脚本加载失败");
  }
  
} catch (error) {
  console.error("调用简化任务同步脚本时出错:", error);
  new Notice(`❌ 执行失败: ${error.message}`);
}

// 返回空字符串
return "";
%>
