<%*
// 测试任务同步功能
// 功能：调用测试版本的任务同步脚本

try {
  console.log("开始调用测试任务同步脚本...");

  // 调用测试脚本
  const testScript = tp.user["test-sync-task"];
  if (typeof testScript === 'function') {
    await testScript(tp);
  } else {
    console.error("测试脚本不是函数:", typeof testScript);
    new Notice("❌ 测试脚本加载失败");
  }

  console.log("测试脚本调用完成");

} catch (error) {
  console.error("调用测试脚本时出错:", error);
  new Notice(`❌ 测试失败: ${error.message}`);
}

// 返回空字符串，不在文档中插入内容
return "";
%>
